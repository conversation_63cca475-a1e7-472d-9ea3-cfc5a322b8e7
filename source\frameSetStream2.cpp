﻿#include "FrameSetStream2.h"

#include "SimpleFrameSetConsumer.h"
#include "VisionTK/IFrameSetStream.h"
#include "VisionTK/Macros.h"
#include "VisionTK/Memory/Ptr.h"

namespace ucv {
namespace media {
FrameSetStream2 FrameSetStream2::New(VisionTK::Camera camera, StdFuncVtkFrameSetRefCallback vtk_frame_set_cb,
                                     VisionTK::Status* status) {
  VTK_CHECK_AND_RETURN_RESULT_WITH_STATUS(camera.Invalid(), FrameSetStream2(), status, VisionTK::Status::CameraInvalid);
  VisionTK::FrameBuffer fb = camera.GetFrameBuffer();

  VTK_CHECK_AND_RETURN_RESULT_WITH_STATUS(fb.Invalid(), FrameSetStream2(), status,
                                          VisionTK::Status::FrameBufferInvalid);

  VisionTK::RefPtr<SimpleFrameSetConsumer> stream = VisionTK::RefPtr<SimpleFrameSetConsumer>::Make();
  stream->SetCallback(vtk_frame_set_cb);
  VisionTK::FrameSetConsumer consumer(stream.Clone());
  fb.AddFrameSetConsumer(consumer);
  return FrameSetStream2(stream);
}

FrameSetStream2::FrameSetStream2(VisionTK::RefPtr<VisionTK::IFrameSetConsumer>&& ptr) : _ptr(ptr.Detach()) {}

VisionTK::IFrameSetConsumer* FrameSetStream2::Ptr() const noexcept { return _ptr; }

FrameSetStream2::FrameSetStream2(const FrameSetStream2& other)
    : _ptr(VisionTK::RefPtrHolder<VisionTK::IFrameSetConsumer>(other._ptr).Attach().Detach()) {}

FrameSetStream2::FrameSetStream2(FrameSetStream2&& other) : _ptr(other._ptr) { other._ptr = nullptr; }

FrameSetStream2& FrameSetStream2::operator=(const FrameSetStream2& other) {
  if (_ptr == other._ptr) {
    return *this;
  }

  VisionTK::RefPtrHolder<VisionTK::IFrameSetConsumer>(_ptr).Detach();
  _ptr = VisionTK::RefPtrHolder<VisionTK::IFrameSetConsumer>(other._ptr).Attach().Detach();

  return *this;
}

FrameSetStream2& FrameSetStream2::operator=(FrameSetStream2&& other) {
  if (this == &other) {
    return *this;
  }

  VisionTK::RefPtrHolder<VisionTK::IFrameSetConsumer>(_ptr).Detach();

  _ptr = other._ptr;
  other._ptr = nullptr;

  return *this;
}

FrameSetStream2::~FrameSetStream2() { VisionTK::RefPtrHolder<VisionTK::IFrameSetConsumer>(_ptr).Detach(); }

bool FrameSetStream2::Invalid() const noexcept { return _ptr == nullptr; }

FrameSetStream2::operator bool() const noexcept { return _ptr != nullptr; }

bool operator==(const FrameSetStream2& lhs, const FrameSetStream2& rhs) { return lhs._ptr == rhs._ptr; }
}  // namespace media
}  // namespace ucv