﻿#include "ucv/cameraapi/rawPixelBuffer.h"
#include "ucv/base/registration.h"
#include "ucv/base/serialization/sharedMemory.h"

namespace ucv {
namespace media {
RawPixelBuffer::RawPixelBuffer(uint32_t width, uint32_t height, uint32_t stride, PixelFormat format,
                               uint32_t bitsPerPixel, ucv::RawBuffer& buffer)
    : PixelBuffer(width, height, format, stride, bitsPerPixel), data_buffer_(std::move(buffer)) {}

const uint8_t* RawPixelBuffer::data() const { return data_buffer_.data(); }

Parcel& RawPixelBuffer::Serialize(Parcel& parcel) {
  PixelBuffer::Serialize(parcel);
  // parcel.Write("data", _mat.data);
  parcel.Write("size", data_buffer_.size());
  parcel.Write("alignment", 1);
  parcel.WriteSharedMemory("bufferObject", data(), data_buffer_.size());

  return parcel;
}

Parcel& RawPixelBuffer::Deserialize(Parcel& parcel) {
  PixelBuffer::Deserialize(parcel);
  // uint8_t* data;
  size_t size;
  size_t alignment;
  // parcel.Read("data", data);
  parcel.Read("size", size);
  parcel.Read("alignment", alignment);

  uint32_t width, height, stride, bitsPerPixel;
  parcel.Read("width", width);
  parcel.Read("height", height);
  int __pixelFormat;
  parcel.Read("pixelFormat", __pixelFormat);
  PixelFormat pixelFormat = (PixelFormat)__pixelFormat;
  parcel.Read("stride", stride);
  parcel.Read("bitsPerPixel", bitsPerPixel);

  std::shared_ptr<SharedMemory> mem;
  parcel.ReadSharedMemory("bufferObject", mem);
  if (!mem) {
    return parcel;
  }

  auto memSize = mem->GetSize();
  if (memSize != size) {
    return parcel;
  }

  data_buffer_.SetSize(memSize);
  // 复制数据
  data_buffer_.SetData(reinterpret_cast<unsigned char*>(mem->Get()), memSize);

  return parcel;
}

}  // namespace media
}  // namespace ucv

using namespace ucv::media;
UCV_REGISTRATION { ucv::Registration::Class<RawPixelBuffer>("RawPixelBuffer"); }
