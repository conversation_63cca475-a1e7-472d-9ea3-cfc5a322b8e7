﻿#pragma once

#include "ucv/datamodel/image.h"
#include "ucv/cameraapi/rawPixelBuffer.h"
#include "ucv/cameraapi/dataModel.h"

namespace ucv {
namespace media {
RefPtr<ucv::Image> ConvertToBgrImage(const uint8_t* data, int width, int height, RawPixelFormat input_pixel_format);
RefPtr<ucv::Image> ConvertToRawDepthImage(const uint8_t* data, int width, int height, RawPixelFormat input_pixel_format);
RefPtr<ucv::Image> RenderDepthImage(RefPtr<ucv::Image> refp_raw_depth, float min_depth = 0.0f, float max_depth = 5000.0f);
RefPtr<ucv::Image> GetIrImage(const uint8_t* data, int width, int height, RawPixelFormat input_pixel_format);
RefPtr<ucv::Image> ConvertGray16To8(RefPtr<ucv::Image> refp_gray16);
RefPtr<ucv::Image> ConvertDepth16To32F(RefPtr<ucv::Image> refp_depth16);

/**
 * @brief 矫正原始的灰度图
 * @param refp_ir_image 原始的灰度图。此处的“原始”指图像画面内容和宽高，而非像素格式等
 */
RefPtr<ucv::Image> RectifyRawIrImage(RefPtr<ucv::Image> refp_ir_image, CalibInfo ir_calib, CameraRotation ir_rotation,
                                     CameraIntrinsic depth_intrisic);

}  // namespace media
}  // namespace ucv
