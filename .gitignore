# Prerequisites  
*.d  

# Compiled Object files  
*.slo  
*.lo  
*.o  
*.obj  

# Precompiled Headers  
*.gch  
*.pch  

# Compiled Dynamic libraries  
*.so  
*.dylib  
*.dll  

# Fortran module files  
*.mod  
*.smod  

# Compiled Static libraries  
*.lai  
*.la  
*.a  
*.lib  

# Executables  
*.exe  
*.out  
*.app  

# Visual Studio specific  
.vs/  
ipch/  
*.aps  
*.ncb  
*.opendb  
*.opensdf  
*.sdf  
*.cachefile  
*.VC.db  
*.VC.VC.opendb  

# User-specific files  
*.rsuser  
*.suo  
*.user  
*.userosscache  
*.sln.docstates  

# Build results  
[Dd]ebug/  
[Dd]ebugPublic/  
[Rr]elease/  
[Rr]eleases/  
x64/  
x86/  
[Ww][Ii][Nn]32/  
[Aa][Rr][Mm]/  
[Aa][Rr][Mm]64/  
bld/  
[Bb]in/  
[Oo]bj/  
[Ll]og/  
[Ll]ogs/
[Bb]uild/

# Visual Studio cache files  
# files ending in .cache can be ignored  
*.[Cc]ache  
# but keep track of directories ending in .cache  
!?*.[Cc]ache/  

# Others  
*.swp  
*~  
*.dbmdl  
*.dbproj.schemaview  
*.pfx  
*.publishsettings  
orleans.codegen.cs  

# Visual C++ cache files  
*.aps  
*.ncb  
*.opendb  
*.opensdf  
*.sdf  
*.cachefile  
*.VC.db  
*.VC.VC.opendb  

# Visual Studio code coverage results  
*.coverage  
*.coveragexml  

# Click-Once directory  
publish/  

# NuGet Packages  
*.nupkg  
*.snupkg  
# The packages folder can be ignored because of Package Restore  
**/[Pp]ackages/*  
# except build/, which is used as an MSBuild target.  
!**/[Pp]ackages/build/  