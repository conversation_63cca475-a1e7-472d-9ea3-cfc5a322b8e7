﻿#include "ucv/cameraapi/cameraFeature.h"

#include "ucv/cameraapi/cameraApiUtils.h"
#include "ucv/cameraapi/jsonConstants.h"

#include <rapidjson/prettywriter.h>
namespace ucv {
namespace media {

RefPtr<CameraFeature> CameraFeature::DeserializeFromJson(const rapidjson::Value& feature_json) {
  RefPtr<CameraFeature> ret_val = MakeRefCounted<CameraFeature>();
  // 解析基本属性
  ret_val->id = feature_json[kId].GetInt();
  ret_val->group_name = feature_json[kGroupName].GetString();
  ret_val->name = feature_json[kName].GetString();
  ret_val->access_mode = static_cast<CameraFeatureAccessMode>(feature_json[kAccessMode].GetInt());
  ret_val->can_write_on_streaming = feature_json[kCanWriteOnStreaming].GetBool();
  // 解析 value
  ret_val->refp_value = CameraFeatureValue::DeserializeFromJson(feature_json[kValue]);

  return ret_val;
}

rapidjson::Value CameraFeature::SerializeToJsonObj(rapidjson::Document::AllocatorType& allocator) {
  rapidjson::Value ret_val(rapidjson::kObjectType);

  // ID
  ret_val.AddMember(kId, id, allocator);
  // Group name
  ret_val.AddMember(kGroupName, rapidjson::Value(group_name.c_str(), allocator).Move(), allocator);
  // Name
  ret_val.AddMember(kName, rapidjson::Value(name.c_str(), allocator).Move(), allocator);
  // Access mode
  ret_val.AddMember(kAccessMode, static_cast<int32_t>(access_mode), allocator);
  // Can write when streaming
  ret_val.AddMember(kCanWriteOnStreaming, can_write_on_streaming, allocator);
  // Value
  ret_val.AddMember(kValue, refp_value->SerializeToJson(allocator), allocator);

  return ret_val;
}

const std::string& CameraFeature::GetJsonStr() {
  rapidjson::Document doc;
  auto& allocator = doc.GetAllocator();
  auto json_value = SerializeToJsonObj(allocator);
  rapidjson::StringBuffer buffer;
  rapidjson::Writer<rapidjson::StringBuffer> writer(buffer);
  json_value.Accept(writer);
  json_str_ = buffer.GetString();
  return json_str_;
}
}  // namespace media
}  // namespace ucv
