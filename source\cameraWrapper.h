﻿#pragma once

#include "VisionTK/Camera.h"
#include "frameSetStream2.h"
#include "ucv/base/object.h"
#include "ucv/base/serialization/sharedMemory.h"
#include "ucv/rpc/methodResult.h"

#include <memory>
#include <rapidjson/document.h>

namespace ucv {
namespace media {
class DLL_EXPORT ClientSession : public ucv::Object {
  UCV_DECLARE_CLASS(ClientSession, ucv::Object);

 public:
  ClientSession(std::string session_key) {
    session_key_ = session_key;
    need_frame_set = false;
  }
  std::shared_ptr<MethodResult<rapidjson::Document>> client_result;
  std::unordered_map<std::string, std::shared_ptr<SharedMemory>> shared_memory_pool;
  bool need_frame_set;

 private:
  std::string session_key_;
};

class DLL_EXPORT CameraWrapper : public ucv::Object {
  UCV_DECLARE_CLASS(CameraWrapper, ucv::Object);

 public:
  CameraWrapper() : opened_by_service(false) {

  }
  ucv::media::FrameSetStream2 frameset_stream;
  // VisionTK::FrameSetStream frameset_stream_old;
  VisionTK::Camera cam;
  std::unordered_map<int, VisionTK::Feature> feature_map;
  std::unordered_map<std::string, RefPtr<ClientSession>> client_ss_map;
  bool opened_by_service;
};
}  // namespace media
}  // namespace ucv
