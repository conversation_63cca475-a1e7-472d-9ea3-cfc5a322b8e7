﻿#pragma once
#include "cameraCoreExport.h"
#include "ucv/rpc/methodChannel.h"
#include "ucv/rpc/transportFactory.h"
#include "ucv/serviceapi/serviceApi.h"
#include "ucv/base/mutex.h"

#include <memory>
#include <mutex>
#include <rapidjson/document.h>

namespace ucv {
namespace media {
class ServiceCoreResource;
 
class CAMERA_CORE_API ChannelManager {
 private:
  // 私有构造函数
  ChannelManager();

 public:
  // 删除拷贝和赋值操作
  ChannelManager(const ChannelManager&) = delete;
  ChannelManager& operator=(const ChannelManager&) = delete;

  // 获取单例实例
  static ChannelManager& GetInstance();

  void Init();
  void Release();

  void InvokeMethod(const std::string& method, std::unique_ptr<rapidjson::Document> arguments,
                    std::unique_ptr<MethodResult<rapidjson::Document>> result = nullptr);

 private:
  static ChannelManager* instance_;
  static std::once_flag creation_flag_;

 private:
  std::unique_ptr<ServiceCoreResource> p_service_core_;
  Mutex init_mutex_;
  int init_cnt_;
};

}  // namespace media
}  // namespace ucv
