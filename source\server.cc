﻿#include <iostream>
#include <thread>
#include "CameraCapture.hpp"
#include "CameraProvider/CameraProvider_VCam.h"
#include "mojo_server.h"

#include "camera_service_json_handle.hpp"
#include "service_core/service_core/service_core.h"


BaseJsonMessageHandle* cameraServiceJsonHandlefactory(){
  return new CameraServiceJsonHandle();
}

int main()
{
	//增加向service_manager注册过程
	ServiceCore core;
	core.initializeIPC();
	core.registerService(service_name);
	// MojoServer::getInstance().SetJsonMessageHandle(new CameraServiceJsonHandle); 
	// 为了支撑服务端可多客户端连接，改成注入工厂函数，每次进来一个客户端连接会调用一次
	MojoServer::getInstance().SetJsonMessageHandleFactory(cameraServiceJsonHandlefactory);
	MojoServer::getInstance().init(service_name);

	int a;
	std::cin>>a;
	core.unregisterService(service_name);
	
}