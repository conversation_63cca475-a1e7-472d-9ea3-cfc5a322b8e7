﻿#include "ucv/cameraapi/directCameraCapture.h"

#include "ucv/cameraapi/cameraLogging.h"
#include "ucv/cameraapi/channelManager.h"
#include "ucv/cameraapi/rawPixelBuffer.h"
// #include "ucv/aaldriver/imageProc.h"
#include "deviceInfoManager.h"
#include "rapidjson/document.h"
#include "rapidjson/stringbuffer.h"  // 用于将JSON转换为字符串
#include "rapidjson/writer.h"        // 用于写入JSON
#include "ucv/base/serialization/sharedMemory.h"
#include "ucv/base/time/timePoint.h"
#include "ucv/cameraapi/cameraApiConfig.h"
#include "ucv/cameraapi/cameraApiUtils.h"
#include "ucv/cameraapi/jsonConstants.h"
#include "ucv/cameraapi/postProcess.h"
#include "ucv/datamodel/image.h"

#include <rapidjson/prettywriter.h>
#include <string>

namespace ucv {
namespace media {
Array<RefPtr<CameraFeature>> DirectCameraCapture::GetFeatures() {
  Array<RefPtr<CameraFeature>> ret_val;
  return ret_val;
}

CameraApiStatus DirectCameraCapture::SetFeature(int feat_id, RefPtr<CameraFeatureValue> refp_feat_val) {
  // TODO
  return CameraApiStatus::Success;
}

CameraApiStatus DirectCameraCapture::SetFeature(int feat_id, std::string json_str_val) {
  rapidjson::Document doc;
  doc.Parse(json_str_val.c_str());

  RefPtr<CameraFeatureValue> refp_feat_val = CameraFeatureValue::DeserializeFromJson(doc);
  return SetFeature(feat_id, refp_feat_val);
}

DirectCameraCapture::DirectCameraCapture(std::string sn) { sn_ = sn; }

CameraStatus DirectCameraCapture::GetCameraStatus() { return CameraStatus::kInvalid; }

void DirectCameraCapture::SetFrameSetCb(StdFuncFrameSetRefCallback cb) {
  std::lock_guard<std::mutex> lock(frame_set_cb_mutex_);
  frame_set_cb_ = cb;
}

CameraApiStatus DirectCameraCapture::Connect(std::string session_key) {
  if (session_key.empty()) {
    TimePoint time_point = TimePoint::Now();
    session_key = std::to_string(time_point());
  }

  VisionTK::Status vtk_status;
  if (cam_.Invalid()) {
    bool is_found = false;
    for (VisionTK::DeviceInfo& devInfo : DeviceInfoManager::GetInstance().cached_deviceinfo_list) {
      if (sn_ == devInfo.GetDeviceSN()) {
        is_found = true;
        cam_ = VisionTK::Camera::ForDevice(devInfo, &vtk_status);
        if (vtk_status != VisionTK::Status::OK) {
          return VtkStatusToCameraApiStatus(vtk_status);
        }
      }
    }
    if (!is_found) {
      return CameraApiStatus::NotExist;
    }
  }

  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  if (cam_.IsOpened()) {
    return CameraApiStatus::Success;
  } else {
    VisionTK::Status vtk_status = cam_.OpenDevice();
    if (vtk_status != VisionTK::Status::OK) {
      return VtkStatusToCameraApiStatus(vtk_status);
    }

    // new stream
    if (frameset_stream_.Invalid()) {
      // TODO: check stream new status?
      frameset_stream_ = VisionTK::FrameSetStream::New(cam_);
    }
    return CameraApiStatus::Success;
  }

  return CameraApiStatus::Failure;
}

CameraApiStatus DirectCameraCapture::Disconnect() {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  VisionTK::Status vtk_status = cam_.CloseDevice();
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::SetSensorEnabled(SensorType sensor_type, bool enabled) {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  VisionTK::Status vtk_status;
  auto sensor = GetSensor(cam_, sensor_type);
  bool is_enabled = sensor.IsEnabled(&vtk_status);
  if (vtk_status != VisionTK::Status::OK) {
    return VtkStatusToCameraApiStatus(vtk_status);
  }
  if (is_enabled == enabled) {
    return CameraApiStatus::Success;
  }
  vtk_status = sensor.SetEnabled(enabled);
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::IsSensorEnabled(SensorType sensor_type, bool& is_enabled) {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  VisionTK::Status vtk_status;
  auto sensor = GetSensor(cam_, sensor_type);
  is_enabled = sensor.IsEnabled(&vtk_status);
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::SetTriggerMode(TriggerMode trigger_mode) {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  VisionTK::Status vtk_status = cam_.SetTriggerMode(TriggerModeToVtkTriggerMode(trigger_mode));
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::SetFramePerTrigger(int num_frames) {
  // TODO
  return CameraApiStatus::Success;
}

CameraApiStatus DirectCameraCapture::FireSoftwareTrigger() {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  VisionTK::Status vtk_status = cam_.FireSoftwareTrigger();
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::StartCapture() {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  VisionTK::Status vtk_status = cam_.StartCapture();
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::StopCapture() {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }

  VisionTK::Status vtk_status = cam_.StopCapture();
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::GetImageModes(SensorType sensor_type, Array<ImageMode>& image_modes) {
  image_modes.clear();
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }
  auto sensor = GetSensor(cam_, sensor_type);

  VisionTK::Status vtk_status;
  std::vector<VisionTK::ImageMode> vtk_image_modes = sensor.GetImageModes(&vtk_status);
  for (const auto& vtk_mode : vtk_image_modes) {
    image_modes.push_back(VtkImageModeToMediaImageMode(vtk_mode));
  }
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::GetCurrentImageMode(SensorType sensor_type, ImageMode& image_mode) {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }
  auto sensor = GetSensor(cam_, sensor_type);

  VisionTK::Status vtk_status;
  auto vtk_image_mode = sensor.GetImageMode(&vtk_status);
  image_mode = VtkImageModeToMediaImageMode(vtk_image_mode);
  return VtkStatusToCameraApiStatus(vtk_status);
}

CameraApiStatus DirectCameraCapture::SetImageMode(SensorType sensor_type, const ImageMode& mode) {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }
  auto sensor = GetSensor(cam_, sensor_type);

  VisionTK::Status vtk_status = sensor.SetImageMode(MediaImageModeToVtkImageMode(mode));
  return VtkStatusToCameraApiStatus(vtk_status);
}
CameraApiStatus DirectCameraCapture::GetCalibInfo(SensorType sensor_type, CalibInfo& calib_info) {
  if (cam_.Invalid()) {
    return CameraApiStatus::CameraInvalid;
  }
  auto sensor = GetSensor(cam_, sensor_type);

  VisionTK::Status vtk_status;

  auto vtk_calib_info = sensor.GetCalibrationInfo(&vtk_status);
  calib_info = VtkCalibInfoToMedia(vtk_calib_info);
  return VtkStatusToCameraApiStatus(vtk_status);
}
CameraApiStatus DirectCameraCapture::SetUndistortionEnabled(SensorType sensor_type, bool enable) {
  return CameraApiStatus::Success;
}
}  // namespace media
}  // namespace ucv