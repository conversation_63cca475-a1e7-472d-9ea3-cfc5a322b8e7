﻿#pragma once

#include "VisionTK/Camera.h"
#include "ucv/cameraapi/dataModel.h"

namespace ucv {
namespace media {
class DeviceInfoManager {
 public:
  // 删除拷贝和赋值操作
  DeviceInfoManager(const DeviceInfoManager&) = delete;
  DeviceInfoManager& operator=(const DeviceInfoManager&) = delete;

  // 获取单例实例
  static DeviceInfoManager& GetInstance() {
    static DeviceInfoManager instance;
    return instance;
  }

  std::vector<VisionTK::DeviceInfo> EnumerateDevices();

 public:
  std::vector<VisionTK::DeviceInfo> cached_deviceinfo_list;

 private:
  // 私有构造函数
  DeviceInfoManager() = default;
};
}  // namespace media
}  // namespace ucv
