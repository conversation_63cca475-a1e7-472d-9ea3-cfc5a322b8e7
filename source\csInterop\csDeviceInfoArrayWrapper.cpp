﻿#include "csDeviceInfoArrayWrapper.h"
#include <iostream>
#include <string>
#include <vector>

using ucv::Array;
using ucv::RefPtr;
namespace ucv {
namespace media {
CsDeviceInfoArrayWrapper::CsDeviceInfoArrayWrapper(const Array<RefPtr<DeviceInfo>>& deviceInfos)
    : m_deviceInfos(deviceInfos) {}

int CsDeviceInfoArrayWrapper::GetCount() const { return m_deviceInfos.size(); }

RefPtr<DeviceInfo> CsDeviceInfoArrayWrapper::GetDeviceInfo(int index) const { return m_deviceInfos[index]; }
}  // namespace media
}  // namespace ucv