﻿#pragma once

#include "deviceInfoManager.h"

#include "VisionTK/DeviceManager.h"

namespace ucv {
namespace media {
std::vector<VisionTK::DeviceInfo> ucv::media::DeviceInfoManager::EnumerateDevices() {
  auto device_manager = VisionTK::DeviceManager::Instance();
  auto device_info_list = device_manager->EnumerateDevices();
  cached_deviceinfo_list = device_info_list;

  return device_info_list;
}

}  // namespace media
}  // namespace ucv
