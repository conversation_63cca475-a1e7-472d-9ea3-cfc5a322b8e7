﻿#pragma once
#include "cameraWrapper.h"
#include "ucv/base/serialization/sharedMemory.h"

#include <string>
#include <unordered_map>

namespace ucv {
namespace media {
class CameraPool {
 public:
  // 删除拷贝和赋值操作
  CameraPool(const CameraPool&) = delete;
  CameraPool& operator=(const CameraPool&) = delete;

  // 获取单例实例
  static CameraPool& GetInstance() {
    static CameraPool instance;
    return instance;
  }

  // 在这里添加你的公共接口...
  /**
   * @brief 用于保存相机的无序map，其中key是sn
   */
  std::unordered_map<std::string, ucv::RefPtr<CameraWrapper>> map_cameras_;
  std::vector<VisionTK::DeviceInfo> cached_deviceinfo_list;

 private:
  // 私有构造函数
  CameraPool() = default;

  // 在这里添加你的成员变量...
};
}  // namespace media
}  // namespace ucv
