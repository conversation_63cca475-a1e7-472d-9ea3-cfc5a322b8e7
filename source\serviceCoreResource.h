﻿#pragma once
#include "ucv/cameraapi/cameraCoreExport.h"
#include "ucv/serviceapi/serviceApi.h"

namespace ucv {
namespace media {
class CAMERA_CORE_API ServiceCoreResource {
 public:
  void Init();
  std::unique_ptr<MethodChannel<rapidjson::Document>> method_channel;

 private:
  ucv::Transport* transport_;
  std::shared_ptr<ucv::TransportFactory> transport_factory_;
  ucv::ServiceApi service_core_;
};
}  // namespace media
}  // namespace ucv