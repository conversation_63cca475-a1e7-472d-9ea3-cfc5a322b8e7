﻿#pragma once

#include "ICameraCapture.h"
#include "cameraCoreExport.h"
#include "dataModel.h"
#include "VisionTK/Camera.h"
#include "ucv/base/mutex.h"
#include "ucv/cameraapi/cameraFeature.h"

#include <functional>
#include <iostream>
#include <string>
#include <vector>

namespace ucv {
namespace media {

class CAMERA_CORE_API DirectCameraCapture : public ICameraCapture/*, public Object*/ {
  //UCV_DECLARE_CLASS(DirectCameraCapture, Object);

 public:
  DirectCameraCapture(std::string sn);
  CameraStatus GetCameraStatus() override;
  void SetFrameSetCb(StdFuncFrameSetRefCallback cb) override;
  /**
   * @brief.
   *
   * \param sn Connect时带一个session key
   * \return
   */
  CameraApiStatus Connect(std::string session_key) override;
  CameraApiStatus Disconnect() override;

  //void GetFrameSet(StdFuncFrameSetRefCallback frame_set_callback) override;

  // CameraApiStatus GetSupportedSensors(std::vector<datamodel::SensorType>& sensor_types);

  // 为了开启采图，最低的要求是开启指定 Sensors 和设置触发模式
  // 另外重要的是分辨率和曝光（自动、手动），往后放放
  CameraApiStatus SetSensorEnabled(SensorType sensor_type, bool enabled) override;
  CameraApiStatus IsSensorEnabled(SensorType sensor_type, bool& is_enabled) override;

  // CameraApiStatus GetTriggerMode(TriggerMode& trigger_mode);
  CameraApiStatus SetTriggerMode(TriggerMode trigger_mode) override;
  CameraApiStatus SetFramePerTrigger(int num_frames) override;
  CameraApiStatus FireSoftwareTrigger() override;

  // Open特定的设备能力（统计哪些设备能力）
  // 如何定义能力如何表达？
  // 打开数据流

  CameraApiStatus StartCapture() override;
  CameraApiStatus StopCapture() override;
  //CameraApiStatus ClearFrameCache() override;
  CameraApiStatus GetImageModes(SensorType sensor_type, Array<ImageMode>& image_modes) override;
  CameraApiStatus GetCurrentImageMode(SensorType sensor_type, ImageMode& image_mode) override;
  CameraApiStatus SetImageMode(SensorType sensor_type, const ImageMode& mode) override;
  CameraApiStatus GetCalibInfo(SensorType sensor_type, CalibInfo& calib_info) override;
  CameraApiStatus SetUndistortionEnabled(SensorType sensor_type, bool enable) override;

  // OnReceiveFrame 用signal形式写，借助signal把图像数据传给API caller
  // 重连机制在core层处理
  // 异常处理：也通过signal处理
  // 重点：面向用户包装 expos等
  // Client 想取图

  ucv::Array<RefPtr<CameraFeature>> GetFeatures() override;
  CameraApiStatus SetFeature(int feat_id, RefPtr<CameraFeatureValue> refp_feat_val) override;
  CameraApiStatus SetFeature(int feat_id, std::string json_str_val) override;
 private:
  std::string sn_;
  std::string session_key_;
  VisionTK::FrameSetStream frameset_stream_;
  VisionTK::Camera cam_;

  StdFuncFrameSetRefCallback frame_set_cb_;
  Mutex frame_set_cb_mutex_;
};
}  // namespace media
}  // namespace ucv
