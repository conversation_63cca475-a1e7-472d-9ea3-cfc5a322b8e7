set(HEADERS)

set(SOURCES
    ${CMAKE_CURRENT_SOURCE_DIR}/main.cpp)

set(THIRDPARTY_DIR ${PROJECT_SOURCE_DIR}/thirdParty)

set(EXAMPLE_PROJECT ucvcameraapi_example)

add_executable(${EXAMPLE_PROJECT} ${HEADERS} ${SOURCES})

find_package(OpenCV REQUIRED)
if (NOT OpenCV_FOUND)
    message(FATAL_ERROR "OpenCV library not found")
else()
    #message(FATAL_ERROR "${OpenCV_INCLUDE_DIRS}")

    target_include_directories(${EXAMPLE_PROJECT} PRIVATE ${OpenCV_INCLUDE_DIRS})
    target_include_directories(${EXAMPLE_PROJECT} PRIVATE ${OpenCV2_INCLUDE_DIRS})
    #include_directories(${OpenCV2_INCLUDE_DIRS})
    link_directories(${OpenCV_LIB_DIR})
    target_link_libraries(${EXAMPLE_PROJECT} PRIVATE ${OpenCV_LIBS})
endif()

set(VisionToolkit_DIR "${UCV_PROJECT_THIRDPARTY_DIR}/VisionToolkit")

target_include_directories(${EXAMPLE_PROJECT}
                            PRIVATE
                           ${CMAKE_CURRENT_SOURCE_DIR}/source
                           ${THIRDPARTY_DIR}
                            ${VisionToolkit_DIR}/include)

target_link_directories(${EXAMPLE_PROJECT}
                        PRIVATE
                        ${VisionToolkit_DIR}/lib)
# Link the libraries
target_link_libraries(${EXAMPLE_PROJECT} PRIVATE
                                        ucvbase
                                        ucvcameraapi
                                        VisionTK.lib)