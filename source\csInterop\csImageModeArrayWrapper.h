﻿#include "ucv/base/array.h"
#include "ucv/base/interop/csExport.h"
#include "ucv/base/interop/csInteropTypes.h"
#include "ucv/base/memory/refPtr.h"
#include "ucv/base/object.h"
#include "ucv/base/refPtr.h"
#include "ucv/cameraapi/dataModel.h"

#include <iostream>
#include <string>
#include <vector>

using ucv::Array;
using ucv::RefPtr;

namespace ucv {
namespace media {
class CsImageModeArrayWrapper : public ucv::Object {
  UCV_DECLARE_CLASS(CsImageModeArrayWrapper, Object);

 public:
  CsImageModeArrayWrapper(const Array<ImageMode>& deviceInfos);

  int GetCount() const;

  ImageMode GetImageMode(int index) const;

 private:
  Array<ImageMode> m_image_modes;
};
}  // namespace media
}  // namespace ucv