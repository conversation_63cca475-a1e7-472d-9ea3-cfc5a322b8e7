cmake_minimum_required(VERSION 3.25)

project(
    ucvcameraservice LANGUAGES C CXX
    VERSION 0.1.0
)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(UCV_ENGINE_CAMERASERVICE_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR})

set(UCV_ENGINE_CAMERASERVICE_INCLUDE_DIR ${UCV_ENGINE_CAMERASERVICE_ROOT_DIR}/include)
set(UCV_ENGINE_CAMERASERVICE_SOURCE_DIR ${UCV_ENGINE_CAMERASERVICE_ROOT_DIR}/source)

set(UCV_ENGINE_CAMERASERVICE_PUBLIC_INCLUDE_DIR ${UCV_ENGINE_CAMERASERVICE_INCLUDE_DIR}/public/ucv/cameraservice)
set(UCV_ENGINE_CAMERASERVICE_INTERNAL_INCLUDE_DIR ${UCV_ENGINE_CAMERASERVICE_INCLUDE_DIR}/internal/ucv/cameraservice)
set(UCV_ENGINE_CAMERASERVICE_PRIVATE_INCLUDE_DIR ${UCV_ENGINE_CAMERASERVICE_SOURCE_DIR})

# Compile source code files
# set(UCV_ENGINE_CAMERASERVICE_PUBLIC_HEADERS
# 	${UCV_ENGINE_CAMERASERVICE_PUBLIC_INCLUDE_DIR}/
# )

#set(UCV_ENGINE_CAMERASERVICE_INTERNAL_HEADERS
#	${UCV_ENGINE_CAMERASERVICE_INTERNAL_INCLUDE_DIR}/
#)

set(UCV_ENGINE_CAMERASERVICE_PRIVATE_HEADERS
	${UCV_ENGINE_CAMERASERVICE_PRIVATE_INCLUDE_DIR}/camera_method_handler.h
	${UCV_ENGINE_CAMERASERVICE_PRIVATE_INCLUDE_DIR}/camera_pool.h
	${UCV_ENGINE_CAMERASERVICE_PRIVATE_INCLUDE_DIR}/cameraWrapper.h
    ${UCV_ENGINE_CAMERASERVICE_PRIVATE_INCLUDE_DIR}/frameSetStream2.h
	${UCV_ENGINE_CAMERASERVICE_PRIVATE_INCLUDE_DIR}/simpleFrameSetConsumer.h
)

set(UCV_ENGINE_CAMERASERVICE_SRCS
	${UCV_ENGINE_CAMERASERVICE_SOURCE_DIR}/main.cc
	${UCV_ENGINE_CAMERASERVICE_SOURCE_DIR}/camera_pool.cpp
	${UCV_ENGINE_CAMERASERVICE_SOURCE_DIR}/camera_method_handler.cpp
    ${UCV_ENGINE_CAMERASERVICE_SOURCE_DIR}/frameSetStream2.cpp
	${UCV_ENGINE_CAMERASERVICE_SOURCE_DIR}/simpleFrameSetConsumer.cpp
)

add_executable(${PROJECT_NAME}
        ${UCV_ENGINE_CAMERASERVICE_PUBLIC_HEADERS}
        ${UCV_ENGINE_CAMERASERVICE_INTERNAL_HEADERS}
        ${UCV_ENGINE_CAMERASERVICE_PRIVATE_HEADERS}
        ${UCV_ENGINE_CAMERASERVICE_SRCS})

target_compile_definitions(${PROJECT_NAME} PRIVATE UCV_CAMERASERVICE_EXPORTS)

target_include_directories(${PROJECT_NAME}
	PUBLIC
	#"$<BUILD_INTERFACE:${UCV_ENGINE_CAMERASERVICE_INCLUDE_DIR}/internal>"
	"$<BUILD_INTERFACE:${UCV_ENGINE_CAMERASERVICE_INCLUDE_DIR}/public>"
	#"$<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/internal>"
	"$<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/public>"
)


set(VisionToolkit_DIR "${UCV_PROJECT_THIRDPARTY_DIR}/VisionToolkit")

target_include_directories(${PROJECT_NAME}
        PRIVATE
        ${THIRDPARTY_DIR}
        ${VisionToolkit_DIR}/include)

target_link_directories(${PROJECT_NAME}
                        PRIVATE
                        ${VisionToolkit_DIR}/lib)
                        
# Link the libraries
target_link_libraries(${PROJECT_NAME}
                        PRIVATE
                        ucvrpc
                        ucvserviceapi
                        ucvcameraapi
                        VisionTK.lib
)


# install: service is executable, no need to install
# install(DIRECTORY ${UCV_ENGINE_CAMERASERVICE_INCLUDE_DIR}/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
 install(TARGETS ${PROJECT_NAME} EXPORT ${CMAKE_PROJECT_NAME}Targets)
 if(MSVC)
     install(FILES $<TARGET_PDB_FILE:${PROJECT_NAME}> DESTINATION ${CMAKE_INSTALL_BINDIR} OPTIONAL)
 endif()
