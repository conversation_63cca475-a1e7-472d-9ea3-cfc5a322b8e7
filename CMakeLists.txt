cmake_minimum_required(VERSION 3.25)

project(
    ucvcameraapi LANGUAGES C CXX
    VERSION 0.1.0
)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

set(UCV_ENGINE_CAMERAAPI_ROOT_DIR ${CMAKE_CURRENT_SOURCE_DIR})

set(UCV_ENGINE_CAMERAAPI_INCLUDE_DIR ${UCV_ENGINE_CAMERAAPI_ROOT_DIR}/include)
set(UCV_ENGINE_CAMERAAPI_SOURCE_DIR ${UCV_ENGINE_CAMERAAPI_ROOT_DIR}/source)

add_subdirectory(source)
add_subdirectory(example)

# install
install(DIRECTORY ${UCV_ENGINE_CAMERAAPI_INCLUDE_DIR}/ DESTINATION ${CMAKE_INSTALL_INCLUDEDIR})
install(TARGETS ${PROJECT_NAME} EXPORT ${CMAKE_PROJECT_NAME}Targets)
if(MSVC)
    install(FILES $<TARGET_PDB_FILE:${PROJECT_NAME}> DESTINATION ${CMAKE_INSTALL_BINDIR} OPTIONAL)
endif()