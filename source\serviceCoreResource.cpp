﻿#include "serviceCoreResource.h"
#include "ucv/cameraapi/jsonConstants.h"
#include "ucv/rpc/transportFactory.h"
#include "ucv/rpc/jsonMethodCodec.h"
#include "ucv/rpc/methodChannel.h"
#include "rapidjson/document.h"

namespace ucv {
namespace media {
void ServiceCoreResource::Init() {
  transport_factory_ = TransportFactory::CreateFactoryFromLibrary("mojoipctransport.dll");

  // 初始化servicecore
  service_core_.InitializeChannel(transport_factory_);

  // 注册服务启动回调函数
  bool flag = false;
  transport_ = transport_factory_->CreateTransport();
  service_core_.AddServiceStartWatch(kCameraServiceName, [&]() {
    transport_->Connect(kCameraServiceName);
    flag = true;
  });

  method_channel = std::make_unique<MethodChannel<rapidjson::Document>>(transport_, &JsonMethodCodec::GetInstance());

  while (!flag) {
  }
}
}  // namespace media
}  // namespace ucv