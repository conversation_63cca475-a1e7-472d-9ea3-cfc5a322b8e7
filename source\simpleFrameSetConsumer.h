﻿#pragma once

#include "VisionTK/Frame.h"
#include "VisionTK/FrameSetQueue.h"
#include "VisionTK/IFrameSetStream.h"
#include "VisionTK/Memory/Ptr.h"

#include <memory>

namespace ucv {
namespace media {
using StdFuncVtkFrameSetRefCallback = std::function<void(VisionTK::FrameSet frame_set)>;

class SimpleFrameSetConsumer : public VisionTK::IFrameSetConsumer {
 public:
  SimpleFrameSetConsumer();
  ~SimpleFrameSetConsumer() override;

 public:
  // IFrameSetConsumer Interface
  VisionTK::Status ReceiveFrameSet(VisionTK::FrameSet frameset) override;
  void SetCallback(StdFuncVtkFrameSetRefCallback cb);

 private:
  friend class VisionTK::RefPtr<SimpleFrameSetConsumer>;
  int32_t IncReferenceCount() override;
  int32_t DecReferenceCount() override;
  void FreeReferencedObject() override;
  VisionTK::ThreadSafeCounter _counter;
  StdFuncVtkFrameSetRefCallback cb_;
};
}  // namespace media
}  // namespace VisionTK