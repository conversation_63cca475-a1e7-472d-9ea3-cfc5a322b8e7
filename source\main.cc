﻿//#include "camera_service_json_handle.h"
#include "camera_method_handler.h"
#include "ucv/rpc/methodChannelServer.h"
#include "ucv/rpc/transportFactory.h"
#include "ucv/serviceapi/serviceApi.h"
#include "ucv/cameraapi/jsonConstants.h"
#include "ucv/base/debugging/backtrace.h"

#include <iostream>
#include <thread>

using namespace ucv::media;

int main() {
  ucv::InstallCrashHandler();
  auto transportFactory = ucv::TransportFactory::CreateFactoryFromLibrary("mojoipctransport.dll");
  ucv::ServiceApi service_core;
  service_core.InitializeChannel(transportFactory);
  service_core.RegisterService(kCameraServiceName);

  ucv::MethodChannelServer server;
  server.SetMethodCallHandler(new CameraMethodHandler);
  server.SetTransportFactory(transportFactory);
  server.Start(kCameraServiceName);
  std::cin.get();

  service_core.UnregisterService(kCameraServiceName);
  server.Stop();
}