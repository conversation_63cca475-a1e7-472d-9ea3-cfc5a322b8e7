set(UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR ${UCV_ENGINE_CAMERAAPI_INCLUDE_DIR}/public/ucv/cameraapi)
set(UCV_ENGINE_CAMERAAPI_INTERNAL_INCLUDE_DIR ${UCV_ENGINE_CAMERAAPI_INCLUDE_DIR}/internal/ucv/cameraapi)
set(UCV_ENGINE_CAMERAAPI_PRIVATE_INCLUDE_DIR ${UCV_ENGINE_CAMERAAPI_SOURCE_DIR})

# [3] 依赖配置
set(THIRDPARTY_DIR ${PROJECT_SOURCE_DIR}/thirdParty)

set(UCV_ENGINE_CAMERAAPI_PUBLIC_HEADERS
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/cameraCapture.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/directCameraCapture.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/cameraCoreExport.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/cameraLogging.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/cameraUtils.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/dataModel.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/ICameraCapture.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/rawPixelBuffer.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/channelManager.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/postProcess.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/frameSet.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/frameInfo.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/frame.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/cameraApiConfig.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/cameraFeature.h
	${UCV_ENGINE_CAMERAAPI_PUBLIC_INCLUDE_DIR}/cameraFeatureValue.h
)

set(UCV_ENGINE_CAMERAAPI_INTERNAL_HEADERS
	${UCV_ENGINE_CAMERAAPI_INTERNAL_INCLUDE_DIR}/jsonConstants.h
	${UCV_ENGINE_CAMERAAPI_INTERNAL_INCLUDE_DIR}/cameraApiUtils.h
)

set(UCV_ENGINE_CAMERAAPI_PRIVATE_HEADERS
	${UCV_ENGINE_CAMERAAPI_PRIVATE_INCLUDE_DIR}/deviceInfoManager.h
	${UCV_ENGINE_CAMERAAPI_PRIVATE_INCLUDE_DIR}/generalSignal.h
	${UCV_ENGINE_CAMERAAPI_PRIVATE_INCLUDE_DIR}/serviceCoreResource.h
	${UCV_ENGINE_CAMERAAPI_PRIVATE_INCLUDE_DIR}/csInterop/csDeviceInfoArrayWrapper.h
	${UCV_ENGINE_CAMERAAPI_PRIVATE_INCLUDE_DIR}/csInterop/csImageModeArrayWrapper.h
	${UCV_ENGINE_CAMERAAPI_PRIVATE_INCLUDE_DIR}/csInterop/csCameraFeatureArrayWrapper.h
)

set(UCV_ENGINE_CAMERAAPI_SRCS
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/cameraApiUtils.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/cameraCapture.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/directCameraCapture.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/cameraLogging.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/cameraUtils.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/generalSignal.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/rawPixelBuffer.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/channelManager.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/postProcess.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/dataModel.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/deviceInfoManager.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/frameSet.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/frameInfo.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/frame.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/cameraApiConfig.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/serviceCoreResource.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/cameraFeature.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/cameraFeatureValue.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/csInterop/csCameraApi.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/csInterop/csDeviceInfoArrayWrapper.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/csInterop/csImageModeArrayWrapper.cpp
	${UCV_ENGINE_CAMERAAPI_SOURCE_DIR}/csInterop/csCameraFeatureArrayWrapper.cpp
)

add_library(${PROJECT_NAME} 
	SHARED
	${UCV_ENGINE_CAMERAAPI_PUBLIC_HEADERS}
	${UCV_ENGINE_CAMERAAPI_INTERNAL_HEADERS}
	${UCV_ENGINE_CAMERAAPI_PRIVATE_HEADERS}
	${UCV_ENGINE_CAMERAAPI_SRCS}
)

find_package(OpenCV REQUIRED)
if (NOT OpenCV_FOUND)
    message(FATAL_ERROR "OpenCV library not found")
else()
    #message(FATAL_ERROR "${OpenCV_INCLUDE_DIRS}")

    target_include_directories(${PROJECT_NAME} PRIVATE ${OpenCV_INCLUDE_DIRS})
    target_include_directories(${PROJECT_NAME} PRIVATE ${OpenCV2_INCLUDE_DIRS})
    #include_directories(${OpenCV2_INCLUDE_DIRS})
    link_directories(${OpenCV_LIB_DIR})
    target_link_libraries(${PROJECT_NAME} PRIVATE ${OpenCV_LIBS})
endif()

target_compile_definitions(${PROJECT_NAME} PRIVATE UCV_CAMERAAPI_EXPORTS)

set(VisionToolkit_DIR "${UCV_PROJECT_THIRDPARTY_DIR}/VisionToolkit")

target_include_directories(${PROJECT_NAME}
	PUBLIC
	"$<BUILD_INTERFACE:${UCV_ENGINE_CAMERAAPI_INCLUDE_DIR}/internal>"
	"$<BUILD_INTERFACE:${UCV_ENGINE_CAMERAAPI_INCLUDE_DIR}/public>"
	"$<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/internal>"
	"$<INSTALL_INTERFACE:${CMAKE_INSTALL_INCLUDEDIR}/public>"
	${VisionToolkit_DIR}/include
    PRIVATE
    ${THIRDPARTY_DIR}
)

target_link_directories(${PROJECT_NAME}
                        PUBLIC
                        ${VisionToolkit_DIR}/lib)

target_link_libraries(${PROJECT_NAME} PUBLIC ucvbase ucvdatamodel ucvserviceapi ucvaaldrv VisionTK.lib)

option(UCV_CAMERAAPI_DIRECT_CALL "Camera API direct call" OFF)
if(UCV_CAMERAAPI_DIRECT_CALL)
	target_compile_definitions(${PROJECT_NAME} PRIVATE USE_DIRECT_CALL)
endif()