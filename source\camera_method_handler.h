﻿#pragma once
#include "camera_pool.h"
#include "ucv/cameraapi/cameraFeature.h"
#include "ucv/cameraapi/cameraFeatureValue.h"
#include "ucv/cameraapi/dataModel.h"
#include "ucv/rpc/methodChannelServer.h"

namespace ucv {
namespace media {
class CameraMethodHandler : public MethodCallHandlerClass<rapidjson::Document> {
 public:
  /**
   * @brief 构造函数.
   *
   */
  CameraMethodHandler();

  void HandleMethodCall(const MethodCall<rapidjson::Document>& call,
                        std::shared_ptr<MethodResult<rapidjson::Document>> result,
                        std::weak_ptr<MethodChannel<rapidjson::Document>> current_channel) override;
 private:
  CameraPool& cam_pool_;

  void Handle_EnumerateDevices(const MethodCall<rapidjson::Document>& call,
                               std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_GetCameraStatus(const MethodCall<rapidjson::Document>& call,
                              std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_Connect(const MethodCall<rapidjson::Document>& call,
                      std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_Disconnect(const MethodCall<rapidjson::Document>& call,
                         std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_SetSensorEnabled(const MethodCall<rapidjson::Document>& call,
                               std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_IsSensorEnabled(const MethodCall<rapidjson::Document>& call,
                              std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_StartCapture(const MethodCall<rapidjson::Document>& call,
                           std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_StopCapture(const MethodCall<rapidjson::Document>& call,
                          std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  /*void Handle_ClearFrameCache(const MethodCall<rapidjson::Document>& call,
                              std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_GetFrameSet(const MethodCall<rapidjson::Document>& call,
                          std::shared_ptr<MethodResult<rapidjson::Document>>& result);*/
  void Handle_SetTriggerMode(const MethodCall<rapidjson::Document>& call,
                             std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_SetFramePerTrigger(const MethodCall<rapidjson::Document>& call,
                                 std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_FireSoftTrigger(const MethodCall<rapidjson::Document>& call,
                              std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_ReleaseImageSharedMemory(const MethodCall<rapidjson::Document>& call,
                                       std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_GetImageModes(const MethodCall<rapidjson::Document>& call,
                            std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_GetCurrentImageMode(const MethodCall<rapidjson::Document>& call,
                                  std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_SetImageMode(const MethodCall<rapidjson::Document>& call,
                           std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_GetCalibInfo(const MethodCall<rapidjson::Document>& call,
                           std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_ReceiveFrameSetLoop(const MethodCall<rapidjson::Document>& call,
                                  std::shared_ptr<MethodResult<rapidjson::Document>>& result,
                                  std::weak_ptr<MethodChannel<rapidjson::Document>> current_channel);
  void Handle_SetUndistortEnabled(const MethodCall<rapidjson::Document>& call,
                                  std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_GetFeatures(const MethodCall<rapidjson::Document>& call,
                          std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_GetFeatureValue(const MethodCall<rapidjson::Document>& call,
                          std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_SetFeature(const MethodCall<rapidjson::Document>& call,
                          std::shared_ptr<MethodResult<rapidjson::Document>>& result);
  void Handle_HasSensor(const MethodCall<rapidjson::Document>& call,
                         std::shared_ptr<MethodResult<rapidjson::Document>>& result);

  void Handle_IsForceNetDeviceIP(const MethodCall<rapidjson::Document>& call,
                                 std::shared_ptr<MethodResult<rapidjson::Document>>& result);

  void SendMethodResult(std::shared_ptr<MethodResult<rapidjson::Document>>& result, rapidjson::Document& json_response,
                        VisionTK::Status vtk_status);
  void SendMethodResult(std::shared_ptr<MethodResult<rapidjson::Document>>& result, rapidjson::Document& json_response,
                        CameraApiStatus status);

  void AddCalibInfoToJsonObject(rapidjson::Value& json_obj, rapidjson::Document& json_doc,
                                const VisionTK::CalibrationInfo& calib_info, float intrinsic_scale_ratio = 1.0);

  bool CanHandleVtkValueType(const VisionTK::Value& vtk_value);

  bool CanHandleVtkFeature(const VisionTK::Feature& vtk_feat);
  void ProcessVtkFeatureGroup(RefPtr<CameraWrapper> cam_wrapper,                            //
                              std::vector<RefPtr<CameraFeature>>&, std::string group_name,  //
                              const std::vector<VisionTK::Feature>& vtk_features);

  RefPtr<CameraFeature> VtkFeatureToMedia(VisionTK::Feature vtk_feature, std::string group_name, int id);
  RefPtr<CameraFeatureValue> VtkValueToMedia(const VisionTK::Value& vtk_value);
  VisionTK::Value MediaValueToVtk(RefPtr<CameraFeatureValue> media_value);
};
}  // namespace media
}  // namespace ucv
