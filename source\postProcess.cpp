﻿#include "ucv/cameraapi/postProcess.h"
#include "ucv/cameraapi/dataModel.h"

#include <opencv2/opencv.hpp>

namespace ucv {
namespace media {
uint32_t GetBitsPerPixel(PixelFormat pixel_format) {
  switch (pixel_format) {
    case ucv::PixelFormat::UNDEFINED:
      return 0;
    case ucv::PixelFormat::DEPTH16:
      return 16;
    case ucv::PixelFormat::DEPTH32F:
      return 32;
    case ucv::PixelFormat::GRAY8:
      return 8;
    case ucv::PixelFormat::GRAY16:
      return 16;
    case ucv::PixelFormat::GRAY32F:
      return 32;
    case ucv::PixelFormat::RGB24:
      return 24;
    default:
      break;
  }
  return 0;
}

uint32_t GetStride(uint32_t width, PixelFormat pixel_format) { return GetBitsPerPixel(pixel_format) / 8 * width; }

RefPtr<ucv::Image> ConvertToBgrImage(const uint8_t* data, int width, int height, RawPixelFormat input_pixel_format) {
  ucv::RawBuffer bgr_buffer;
  if (input_pixel_format == RawPixelFormat::YUYV) {
    // Create Mat for YUYV data (width * 2 because YUYV uses 2 bytes per pixel)
    cv::Mat yuyv_mat(height, width * 2, CV_8UC1, const_cast<uint8_t*>(data));

    // Reshape to proper 2D YUYV format
    cv::Mat yuyv_2d = yuyv_mat.reshape(2, height);  // 2 channels
    cv::Mat bgr_mat;
    // Convert YUYV to BGR (OpenCV default color format)
    cv::cvtColor(yuyv_2d, bgr_mat, cv::COLOR_YUV2BGR_YUYV);

    uint32_t bgr_buffer_size = width * height * 3;
    bgr_buffer = ucv::RawBuffer(bgr_buffer_size);
    // 复制数据
    bgr_buffer.SetData(bgr_mat.data, bgr_buffer_size);
  } else if (input_pixel_format == RawPixelFormat::RGB || input_pixel_format == RawPixelFormat::BGR) {
    uint32_t bgr_buffer_size = width * height * 3;
    bgr_buffer = ucv::RawBuffer(bgr_buffer_size);
    // 复制数据
    bgr_buffer.SetData(data, bgr_buffer_size);
  }

  RefPtr<RawPixelBuffer> pixel_buffer =
      MakeRefCounted<RawPixelBuffer>(width, height, width * 3, PixelFormat::RGB24, 24, bgr_buffer);

  RefPtr<Image> image = MakeRefCounted<Image>(pixel_buffer);
  return image;
}

RefPtr<ucv::Image> ConvertToRawDepthImage(const uint8_t* data, int width, int height,
                                          RawPixelFormat input_pixel_format) {
  uint32_t depth_buffer_size = width * height * 2;

  ucv::RawBuffer depth_buffer(depth_buffer_size);
  if (input_pixel_format == RawPixelFormat::Depth16) {
    // memcpy(depth_buffer.data(), data, depth_buffer_size);
    depth_buffer.SetData(data, depth_buffer_size);
  }

  RefPtr<RawPixelBuffer> pixel_buffer =
      MakeRefCounted<RawPixelBuffer>(width, height, width * 2, PixelFormat::DEPTH16, 16, depth_buffer);
  RefPtr<Image> image = MakeRefCounted<Image>(pixel_buffer);
  return image;
}

RefPtr<ucv::Image> RenderDepthImage(RefPtr<ucv::Image> refp_raw_depth, float min_depth, float max_depth) {
  auto width = refp_raw_depth->width();
  auto height = refp_raw_depth->height();

  // 首先转换为CV_16UC1格式的Mat
  cv::Mat depth_mat(height, width, CV_16UC1, (void*)refp_raw_depth->data());

  // 转换到8位，使用指定范围
  cv::Mat depth_8bit;
  depth_mat.convertTo(depth_8bit, CV_8UC1,
                      255.0 / (max_depth - min_depth),              // scale
                      -255.0 * min_depth / (max_depth - min_depth)  // offset
  );

  // 处理超出范围的值
  cv::Mat mask_too_close = depth_mat < min_depth;      // 太近的点
  cv::Mat mask_too_far = depth_mat > max_depth;        // 太远的点
  cv::Mat mask_invalid = depth_mat == 0;               // 无效点
  depth_8bit.setTo(0, mask_too_close | mask_invalid);  // 近处和无效点设为黑色
  depth_8bit.setTo(255, mask_too_far);                 // 远处设为最大值

  // 应用彩虹色映射
  cv::Mat color;
  cv::applyColorMap(depth_8bit, color, cv::COLORMAP_RAINBOW);

  // 将无效点设为黑色
  color.setTo(cv::Vec3b(0, 0, 0), mask_invalid);

  // 获取数据大小
  size_t data_size = color.total() * color.elemSize();  // total = width * height, elemSize = 3 (BGR)
  ucv::RawBuffer buffer(data_size);
  // 复制数据
  buffer.SetData(color.data, data_size);
  // memcpy(buffer, color.data, data_size);
  RefPtr<RawPixelBuffer> pixel_buffer =
      MakeRefCounted<RawPixelBuffer>(width, height, width * 3, PixelFormat::RGB24, 24, buffer);
  RefPtr<Image> image = MakeRefCounted<Image>(pixel_buffer);
  return image;
}

RefPtr<ucv::Image> GetIrImage(const uint8_t* data, int width, int height, RawPixelFormat input_pixel_format) {
  PixelFormat pixel_format = PixelFormat::UNDEFINED;
  size_t buffer_size = 0;
  uint32_t stride = 0;
  uint32_t bits_per_pixel = 0;
  if (input_pixel_format == RawPixelFormat::Mono16) {
    pixel_format = PixelFormat::GRAY16;
    buffer_size = width * height * 2;
    stride = width * 2;
    bits_per_pixel = 16;
  } else {
    pixel_format = PixelFormat::GRAY8;
    buffer_size = width * height;
    stride = width;
    bits_per_pixel = 8;
  }

  ucv::RawBuffer raw_buffer = ucv::RawBuffer(buffer_size);
  // 复制数据raw_buffer
  raw_buffer.SetData(data, buffer_size);

  RefPtr<RawPixelBuffer> pixel_buffer =
      MakeRefCounted<RawPixelBuffer>(width, height, stride, pixel_format, bits_per_pixel, raw_buffer);
  RefPtr<Image> image = MakeRefCounted<Image>(pixel_buffer);
  return image;
}

RefPtr<ucv::Image> ConvertGray16To8(RefPtr<ucv::Image> refp_gray16) {
  if (refp_gray16->pixelFormat() != PixelFormat::GRAY16) {
    return RefPtr<ucv::Image>();
  }

  auto width = refp_gray16->width();
  auto height = refp_gray16->height();

  cv::Mat gray16_mat(height, width, CV_16UC1, (void*)refp_gray16->data());
  cv::Mat gray8_mat;
  // cv::normalize(gray16_mat, gray8_mat, 0, 255, cv::NORM_MINMAX, CV_8U);
  gray16_mat.convertTo(gray8_mat, CV_8U, 255.0 / 65535.0);

  // 获取数据大小
  size_t num_bytes = gray8_mat.total() * gray8_mat.elemSize();
  ucv::RawBuffer buffer(num_bytes);
  // 复制数据
  buffer.SetData(gray8_mat.data, num_bytes);
  RefPtr<RawPixelBuffer> pixel_buffer =
      MakeRefCounted<RawPixelBuffer>(width, height, width, PixelFormat::GRAY8, 8, buffer);
  RefPtr<Image> image = MakeRefCounted<Image>(pixel_buffer);
  return image;
}

RefPtr<ucv::Image> ConvertDepth16To32F(RefPtr<ucv::Image> refp_depth16) {
  if (refp_depth16->pixelFormat() != PixelFormat::DEPTH16) {
    return RefPtr<ucv::Image>();
  }

  auto width = refp_depth16->width();
  auto height = refp_depth16->height();

  cv::Mat depth16_mat(height, width, CV_16UC1, (void*)refp_depth16->data());
  cv::Mat depth32f_mat;
  float scale_unit = refp_depth16->buffer()->GetScaleUnit();
  if (scale_unit <= 0) {
    scale_unit = 1.0;
  }
  depth16_mat.convertTo(depth32f_mat, CV_32F, scale_unit);

  // 获取数据大小
  size_t num_bytes = depth16_mat.total() * depth16_mat.elemSize();
  ucv::RawBuffer buffer(num_bytes);
  // 复制数据
  buffer.SetData(depth16_mat.data, num_bytes);
  RefPtr<RawPixelBuffer> pixel_buffer =
      MakeRefCounted<RawPixelBuffer>(width, height, width * 4, PixelFormat::DEPTH32F, 32, buffer);
  RefPtr<Image> image = MakeRefCounted<Image>(pixel_buffer);
  return image;
}

RefPtr<ucv::Image> RectifyRawIrImage(RefPtr<ucv::Image> refp_ir_image, CalibInfo ir_calib, CameraRotation ir_rotation,
                                     CameraIntrinsic depth_intrisic) {
  auto width = refp_ir_image->width();
  auto height = refp_ir_image->height();
  int mat_type = 0;
  auto ir_pixel_format = refp_ir_image->buffer()->pixelFormat();
  if (ir_pixel_format == PixelFormat::GRAY8) {
    mat_type = CV_8UC1;
  } else if (ir_pixel_format == PixelFormat::GRAY16) {
    mat_type = CV_16UC1;
  } else if (ir_pixel_format == PixelFormat::GRAY32F) {
    mat_type = CV_32FC1;
  } else {
    return RefPtr<ucv::Image>();
  }

  cv::Mat ir_mat(height, width, mat_type, (void*)refp_ir_image->data());

  cv::Mat oldIntri(3, 3, CV_32FC1, &ir_calib.intrinsic.data);
  cv::Mat distCoef(1, 12, CV_32FC1, &ir_calib.distortion.data);
  cv::Mat rotation(3, 3, CV_32FC1, &ir_rotation.data);
  cv::Mat newIntri(3, 3, CV_32FC1, &depth_intrisic.data);

  cv::Mat map1, map2;
  cv::initUndistortRectifyMap(oldIntri, distCoef, rotation, newIntri, cv::Size(width, height), CV_32FC1, map1, map2);

  cv::Mat recified_ir_mat;
  cv::remap(ir_mat, recified_ir_mat, map1, map2, cv::INTER_LINEAR);

  // 获取数据大小
  size_t num_bytes = recified_ir_mat.total() * recified_ir_mat.elemSize();
  ucv::RawBuffer buffer(num_bytes);
  // 复制数据
  buffer.SetData(recified_ir_mat.data, num_bytes);
  RefPtr<RawPixelBuffer> pixel_buffer =
      MakeRefCounted<RawPixelBuffer>(width, height, GetStride(width, ir_pixel_format),  // width, height, stride
                                     ir_pixel_format, GetBitsPerPixel(ir_pixel_format), buffer);
  RefPtr<Image> image = MakeRefCounted<Image>(pixel_buffer);
  return image;
}
}  // namespace media
}  // namespace ucv
