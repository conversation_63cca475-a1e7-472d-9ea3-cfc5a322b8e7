﻿#pragma once
#include "ucv/base/refPtr.h"
#include "ucv/cameraapi/cameraFeatureValue.h"
#include "ucv/cameraapi/dataModel.h"

#include <rapidjson/document.h>

namespace ucv {
namespace media {
class CameraFeature : public Object {
  UCV_DECLARE_CLASS(CameraFeature, Object);

 public:
  /**
   * @brief 从 JSON 对象中反序列化 Feature.
   */
  static RefPtr<CameraFeature> DeserializeFromJson(const rapidjson::Value& json);

 public:
  int id;
  std::string group_name;
  std::string name;
  CameraFeatureAccessMode access_mode;
  bool can_write_on_streaming;
  RefPtr<CameraFeatureValue> refp_value;

  CameraValueType GetValueType() const { return refp_value->GetValueType(); }

  /**
   * @brief 将 Feature 序列化为 JSON 对象.
   */
  rapidjson::Value SerializeToJsonObj(rapidjson::Document::AllocatorType& allocator);

  const std::string& GetJsonStr();

 private:
  std::string json_str_;
};
}  // namespace media
}  // namespace ucv
