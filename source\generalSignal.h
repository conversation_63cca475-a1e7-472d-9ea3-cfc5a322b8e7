﻿#pragma once

#include "ucv/cameraapi/dataModel.h"

#include <condition_variable>
#include <mutex>

namespace ucv {
namespace media {
template <typename T>
class GeneralSignal {
 public:
  static constexpr std::chrono::milliseconds DEFAULT_TIMEOUT{5000};

 private:
  T data_;
  std::mutex mtx_;
  std::condition_variable cv_;
  bool data_available = false;  // 更清晰地表达有新数据可用

 public:
  void produce(T value) {
    {
      std::unique_lock<std::mutex> lock(mtx_);
      data_ = std::move(value);
      data_available = true;
    }
    cv_.notify_one();
  }

  T consume(std::chrono::milliseconds timeout = DEFAULT_TIMEOUT) {
    bool is_valid;
    return consume(is_valid, timeout);
  }

  T consume(bool& is_valid, std::chrono::milliseconds timeout = DEFAULT_TIMEOUT) {
    std::unique_lock<std::mutex> lock(mtx_);
    if (!cv_.wait_for(lock, timeout, [this] { return data_available; })) {
      is_valid = false;
      return T{};  // 超时返回默认值
    }

    is_valid = true;
    data_available = false;
    return data_;
  }
};

class StatusSignal : public GeneralSignal<CameraApiStatus> {
 public:
  CameraApiStatus consume(std::chrono::milliseconds timeout = DEFAULT_TIMEOUT) {
    bool is_valid;
    auto status = GeneralSignal::consume(is_valid, timeout);
    if (!is_valid) {
      return CameraApiStatus::Timeout;
    }
    return status;
  };
};
}  // namespace media
}  // namespace ucv
