﻿#include "ucv/cameraapi/cameraApiUtils.h"

#include "ucv/cameraapi/jsonConstants.h"
#include <rapidjson/prettywriter.h>

namespace ucv {
namespace media {
CameraApiStatus VtkStatusToCameraApiStatus(VisionTK::Status vtk_status) {
  return static_cast<CameraApiStatus>(static_cast<int32_t>(vtk_status));
}

VisionTK::TriggerMode TriggerModeToVtkTriggerMode(TriggerMode trigger_mode) {
  switch (trigger_mode) {
    case ucv::media::TriggerMode::kOff:
      return VisionTK::TriggerMode::Off;
    case ucv::media::TriggerMode::kHardware:
      return VisionTK::TriggerMode::Slave;
    case ucv::media::TriggerMode::kSoftware:
      return VisionTK::TriggerMode::Slave;
    case ucv::media::TriggerMode::kTriggerMode22:
      return VisionTK::TriggerMode::TriggerMode22;
    default:
      break;
  }
  return VisionTK::TriggerMode::Invalid;
}

int32_t VtkStatusToResponseValue(VisionTK::Status vtk_status) {
  // vtk status => status
  auto status = VtkStatusToCameraApiStatus(vtk_status);
  // status => int32
  return CameraApiStatusToInt32(status);
}

int32_t VtkFrameFlagToResponseValue(VisionTK::FrameFlags frame_flag) {
  switch (frame_flag) {
    case VisionTK::FrameFlags::Color:
      return static_cast<int32_t>(SensorType::kRgb);
    case VisionTK::FrameFlags::Depth:
      return static_cast<int32_t>(SensorType::kDepth);
    case VisionTK::FrameFlags::InfraredLeft:
      return static_cast<int32_t>(SensorType::kIrLeft);
    case VisionTK::FrameFlags::InfraredRight:
      return static_cast<int32_t>(SensorType::kIrRigth);
    default:
      break;
  }
  return -1;
}

ucv::media::SensorType VtkFrameFlagToMediaSensorType(VisionTK::FrameFlags frame_flag) {
  switch (frame_flag) {
    case VisionTK::FrameFlags::Color:
      return SensorType::kRgb;
    case VisionTK::FrameFlags::Depth:
      return SensorType::kDepth;
    case VisionTK::FrameFlags::InfraredLeft:
      return SensorType::kIrLeft;
    case VisionTK::FrameFlags::InfraredRight:
      return SensorType::kIrRigth;
    default:
      break;
  }
  return SensorType::kError;
}

VisionTK::Sensor GetSensor(const VisionTK::Camera& cam, SensorType sensor_type) {
  switch (sensor_type) {
    case ucv::media::SensorType::kDepth:
      return cam.GetDepthSensor();
    case ucv::media::SensorType::kIrLeft:
      return cam.GetInfraredLeftSensor();
    case ucv::media::SensorType::kIrRigth:
      return cam.GetInfraredRightSensor();
    case ucv::media::SensorType::kRgbLeft:
      return cam.GetColorSensor();
    case ucv::media::SensorType::kRgbRight:
      return cam.GetColorSensor();
    default:
      break;
  }
  return VisionTK::Sensor();
}

ucv::media::ImageMode VtkImageModeToMediaImageMode(VisionTK::ImageMode vtk_mode) {
  ucv::media::ImageMode mode;
  mode.width = vtk_mode.width;
  mode.height = vtk_mode.height;
  mode.pixel_format = static_cast<RawPixelFormat>(vtk_mode.format);
  return mode;
}
VisionTK::ImageMode MediaImageModeToVtkImageMode(ucv::media::ImageMode mode) {
  VisionTK::ImageMode ret_val;
  ret_val.width = mode.width;
  ret_val.height = mode.height;
  ret_val.format = static_cast<VisionTK::PixelFormat>(mode.pixel_format);
  return ret_val;
}
ucv::media::CalibInfo VtkCalibInfoToMedia(VisionTK::CalibrationInfo vtk_calib_info) {
  ucv::media::CalibInfo ret_val;

  const auto& intrinsic = vtk_calib_info.GetCameraIntrinsic();
  ret_val.intrinsic.width = intrinsic.width;
  ret_val.intrinsic.height = intrinsic.height;
  const auto& intrinsic_data = intrinsic.data;
  for (int i = 0; i < 9; ++i) {
    ret_val.intrinsic.data[i] = intrinsic_data[i];
  }

  const auto& extrinsic_data = vtk_calib_info.GetCameraExtrinsic().data;
  for (int i = 0; i < 16; ++i) {
    ret_val.extrinsic.data[i] = extrinsic_data[i];
  }

  const auto& dist_data = vtk_calib_info.GetCameraDistortion().data;
  for (int i = 0; i < 12; ++i) {
    ret_val.distortion.data[i] = dist_data[i];
  }

  return ret_val;
}
CameraFeatureAccessMode VtkAccessModeToMedia(VisionTK::AccessMode vtk_access_mode) {
  switch (vtk_access_mode) {
    case VisionTK::AccessMode::Readable:
      return CameraFeatureAccessMode::kReadOnly;
    case VisionTK::AccessMode::Writable:
      return CameraFeatureAccessMode::kWriteOnly;
    case VisionTK::AccessMode::ReadableWritable:
      return CameraFeatureAccessMode::kReadWrite;
  }
  return CameraFeatureAccessMode::kNotAvailable;
}
int32_t VtkAccessModeToResponseValue(VisionTK::AccessMode vtk_access_mode) {
  return static_cast<int32_t>(VtkAccessModeToMedia(vtk_access_mode));
}

void PrintJson(const rapidjson::Value& json_value) {
  std::cout << "-------------------------------" << std::endl;
  rapidjson::StringBuffer buffer;
  rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
  json_value.Accept(writer);
  std::cout << buffer.GetString() << std::endl;
  std::cout << "-------------------------------" << std::endl;
}
}  // namespace media
}  // namespace ucv
