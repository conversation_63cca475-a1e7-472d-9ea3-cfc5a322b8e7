﻿#include "ucv/cameraapi/dataModel.h"
#include <cstring>

namespace ucv {
namespace media {
std::string CameraApiStatusToMessage(CameraApiStatus status) {
  switch (status) {
    case CameraApiStatus::Success:
      return "Success";
    case CameraApiStatus::Failure:
      return "General failure";
    case CameraApiStatus::ArrayInfoInvalid:
      return "Array information is invalid";
    case CameraApiStatus::ArrayInvalid:
      return "Array is invalid";
    case CameraApiStatus::CalibrationInfoInvalid:
      return "Calibration information is invalid";
    case CameraApiStatus::CameraInvalid:
      return "Camera is invalid";
    case CameraApiStatus::ComponentInvalid:
      return "Component is invalid";
    case CameraApiStatus::DeviceInvalid:
      return "Device is invalid";
    case CameraApiStatus::DeviceError:
      return "Device error occurred";
    case CameraApiStatus::DeviceIdle:
      return "Device is idle";
    case CameraApiStatus::DeviceBusy:
      return "Device is busy";
    case CameraApiStatus::DeviceLost:
      return "Device connection lost";
    case CameraApiStatus::DeviceInterfaceInvalid:
      return "Device interface is invalid";
    case CameraApiStatus::DeviceInterfaceTypeError:
      return "Device interface type error";
    case CameraApiStatus::DeviceInfoInvalid:
      return "Device information is invalid";
    case CameraApiStatus::FeatureInvalid:
      return "Feature is invalid";
    case CameraApiStatus::FeatureInfoInvalid:
      return "Feature information is invalid";
    case CameraApiStatus::FeatureTypeError:
      return "Feature type error";
    case CameraApiStatus::FrameInvalid:
      return "Frame is invalid";
    case CameraApiStatus::FrameMetadataInvalid:
      return "Frame metadata is invalid";
    case CameraApiStatus::FrameBufferInvalid:
      return "Frame buffer is invalid";
    case CameraApiStatus::FrameBufferConsumerInvalid:
      return "Frame buffer consumer is invalid";
    case CameraApiStatus::FrameSetInvalid:
      return "Frame set is invalid";
    case CameraApiStatus::FrameSetStreamInvalid:
      return "Frame set stream is invalid";
    case CameraApiStatus::FrameSetConsumerInvalid:
      return "Frame set consumer is invalid";
    case CameraApiStatus::TriggerModeError:
      return "Trigger mode error";
    case CameraApiStatus::NotExist:
      return "Resource does not exist";
    case CameraApiStatus::NotImplemented:
      return "Function not implemented";
    case CameraApiStatus::NotPermitted:
      return "Operation not permitted";
    case CameraApiStatus::NotSupported:
      return "Operation not supported";
    case CameraApiStatus::OutOfMemory:
      return "Out of memory";
    case CameraApiStatus::OutOfIndexRange:
      return "Index out of range";
    case CameraApiStatus::OutOfValueRange:
      return "Value out of range";
    case CameraApiStatus::ParameterInvalid:
      return "Parameter is invalid";
    case CameraApiStatus::StructureInfoInvalid:
      return "Structure information is invalid";
    case CameraApiStatus::StructureInvalid:
      return "Structure is invalid";
    case CameraApiStatus::Timeout:
      return "Operation timeout";
    case CameraApiStatus::ValueInvalid:
      return "Value is invalid";
    case CameraApiStatus::ValueTypeError:
      return "Value type error";
    case CameraApiStatus::ValueInfoInvalid:
      return "Value information is invalid";
    default:
      return "Unknown status code";
  }
}  

int32_t CameraApiStatusToInt32(CameraApiStatus status) { return static_cast<int32_t>(status); }

CameraApiStatus Int32ToCameraApiStatus(int32_t status_code) { return static_cast<CameraApiStatus>(status_code); }

DeviceInfoC GetDeviceInfoC(RefPtr<DeviceInfo> refp_device_info) {
  DeviceInfoC ret_val;

  ret_val.interface_type = refp_device_info->interface_type;
  ret_val.usb_info = refp_device_info->usb_info;
  ret_val.status_code = refp_device_info->status_code;
  ret_val.is_opened_by_service = refp_device_info->is_opened_by_service ? 1 : 0;
  // network info
  const auto& network_info = refp_device_info->network_info;
  auto& network_info_c = ret_val.network_info;

#ifdef UCV_OS_LINUX
  strncpy(network_info_c.mac, network_info.mac.c_str(), sizeof(network_info_c.mac) - 1);
  strncpy(network_info_c.ip, network_info.ip.c_str(), sizeof(network_info_c.ip) - 1);
  strncpy(network_info_c.netmask, network_info.netmask.c_str(), sizeof(network_info_c.netmask) - 1);
  strncpy(network_info_c.gateway, network_info.gateway.c_str(), sizeof(network_info_c.gateway) - 1);
  strncpy(network_info_c.broadcast, network_info.broadcast.c_str(), sizeof(network_info_c.broadcast) - 1);
  // other members
  strncpy(ret_val.sn, refp_device_info->sn.c_str(), sizeof(ret_val.sn) - 1);
  strncpy(ret_val.name, refp_device_info->name.c_str(), sizeof(ret_val.name) - 1);
  strncpy(ret_val.model_name, refp_device_info->model_name.c_str(), sizeof(ret_val.model_name) - 1);
  strncpy(ret_val.vendor_name, refp_device_info->vendor_name.c_str(), sizeof(ret_val.vendor_name) - 1);
  strncpy(ret_val.firmware_version, refp_device_info->firmware_version.c_str(), sizeof(ret_val.firmware_version) - 1);
  strncpy(ret_val.config_version, refp_device_info->config_version.c_str(), sizeof(ret_val.config_version) - 1);
  strncpy(ret_val.interface_ip, refp_device_info->interface_IP.c_str(), sizeof(ret_val.interface_ip) - 1);
#else
  strncpy_s(network_info_c.mac, network_info.mac.c_str(), sizeof(network_info_c.mac) - 1);
  strncpy_s(network_info_c.ip, network_info.ip.c_str(), sizeof(network_info_c.ip) - 1);
  strncpy_s(network_info_c.netmask, network_info.netmask.c_str(), sizeof(network_info_c.netmask) - 1);
  strncpy_s(network_info_c.gateway, network_info.gateway.c_str(), sizeof(network_info_c.gateway) - 1);
  strncpy_s(network_info_c.broadcast, network_info.broadcast.c_str(), sizeof(network_info_c.broadcast) - 1);
  // other members
  strncpy_s(ret_val.sn, refp_device_info->sn.c_str(), sizeof(ret_val.sn) - 1);
  strncpy_s(ret_val.name, refp_device_info->name.c_str(), sizeof(ret_val.name) - 1);
  strncpy_s(ret_val.model_name, refp_device_info->model_name.c_str(), sizeof(ret_val.model_name) - 1);
  strncpy_s(ret_val.vendor_name, refp_device_info->vendor_name.c_str(), sizeof(ret_val.vendor_name) - 1);
  strncpy_s(ret_val.firmware_version, refp_device_info->firmware_version.c_str(), sizeof(ret_val.firmware_version) - 1);
  strncpy_s(ret_val.config_version, refp_device_info->config_version.c_str(), sizeof(ret_val.config_version) - 1);
  strncpy_s(ret_val.interface_ip, refp_device_info->interface_IP.c_str(), sizeof(ret_val.interface_ip) - 1);
#endif

  return ret_val;
}

RefPtr<DeviceInfo> DeviceInfo::New() { return MakeRefCounted<DeviceInfo>(); }

std::string DeviceInfo::ToString() const {
  {
    std::stringstream ss;
    ss << "DeviceInfo {\n";
    ss << "  SN: " << sn << "\n";
    ss << "  Name: " << name << "\n";
    ss << "  Model: " << model_name << "\n";
    ss << "  Vendor: " << vendor_name << "\n";
    ss << "  Firmware: " << firmware_version << "\n";
    ss << "  Config Version: " << config_version << "\n";
    ss << "  Status code: " << status_code << "\n";

    ss << "  Interface: ";
    switch (interface_type) {
      case InterfaceType::None:
        ss << "None\n";
        break;
      case InterfaceType::USB:
        ss << "USB {\n";
        ss << "    Bus: " << usb_info.bus << "\n";
        ss << "    Address: " << usb_info.address << "\n";
        ss << "  }\n";
        break;
      case InterfaceType::Network:
        ss << "Network {\n";
        ss << "    MAC: " << network_info.mac << "\n";
        ss << "    IP: " << network_info.ip << "\n";
        ss << "    Netmask: " << network_info.netmask << "\n";
        ss << "    Gateway: " << network_info.gateway << "\n";
        ss << "    Broadcast: " << network_info.broadcast << "\n";
        ss << "  }\n";
        break;
    }
    ss << "}";
    return ss.str();
  }
}

std::string SensorTypeToString(SensorType sensor_type) {
  switch (sensor_type) {
    case ucv::media::SensorType::kDepth:
      return "depth";
    case ucv::media::SensorType::kIrLeft:
      return "IR left";
    case ucv::media::SensorType::kIrRigth:
      return "IR right";
    case ucv::media::SensorType::kRgbLeft:
      return "RGB left";
    case ucv::media::SensorType::kRgbRight:
      return "RGB right";
  }
  return "error sensor type";
}
}  // namespace media
}  // namespace ucv
