﻿#include "ucv/base/refPtr.h"
#include "ucv/cameraapi/cameraApiUtils.h"
#include "ucv/cameraapi/cameraCapture.h"
#include "ucv/cameraapi/cameraLogging.h"
#include "ucv/cameraapi/cameraUtils.h"
#include "ucv/cameraapi/channelManager.h"
#include "ucv/cameraapi/frameSet.h"
#include "ucv/cameraapi/postProcess.h"
#include "ucv/datamodel/image.h"
#include "ucv/serviceapi/serviceApi.h"

#include <iostream>
#include <memory>
#include <opencv2/opencv.hpp>
#include <thread>
#include <ucv/base/array.h>

// using namespace ucv;
using namespace ucv::media;
using std::cin;
using std::cout;
using std::endl;
using ucv::RefPtr;

int iddd = 1;

void ReceiveFrameSetCallback(ucv::RefPtr<FrameSet> refp_frame_set) {
  ucv::cameraLogger.log(ucv::LogLevel::Info, "", "============== Receive frame set callback: %d", iddd);

  auto refp_depth_frame = refp_frame_set->GetFrame(SensorType::kDepth);
  auto depth_frame_info = refp_depth_frame->frame_info;
  auto refp_depth_image = refp_depth_frame->image;
  auto rendered_image = ucv::media::RenderDepthImage(refp_depth_image);
  cv::Mat mat_rendered_depth(rendered_image->height(), rendered_image->width(), CV_8UC3, (void*)rendered_image->data());
  std::string filename_depth = std::string("depth_") + std::to_string(iddd) + ".png";
  cv::imwrite(filename_depth, mat_rendered_depth);

  ucv::cameraLogger.log(ucv::LogLevel::Info, "", "depth: %d x %d", refp_depth_image->width(),
                        refp_depth_image->height());

  auto refp_rgb_frame = refp_frame_set->GetFrame(SensorType::kRgb);
  if (refp_rgb_frame) {
    auto rgb_frame_info = refp_rgb_frame->frame_info;
    auto refp_rgb_image = refp_rgb_frame->image;
    cv::Mat mat_rgb(refp_rgb_image->height(), refp_rgb_image->width(), CV_8UC3, (void*)refp_rgb_image->data());
    std::string filename_rgb = std::string("color_") + std::to_string(iddd) + ".png";
    cv::imwrite(filename_rgb, mat_rgb);

    ucv::cameraLogger.log(ucv::LogLevel::Info, "", "RGB: %d x %d", refp_rgb_image->width(), refp_rgb_image->height());
  }

  ++iddd;
}

void PrintCameraStatus(const std::unique_ptr<CameraCapture>& cc) {
  CameraStatus cam_status = cc->GetCameraStatus();
  ucv::LogLevel log_level = ucv::LogLevel::Info;
  std::string status_message = "+++++++ ";
  switch (cam_status) {
    case ucv::media::CameraStatus::kOpened:
      log_level = ucv::LogLevel::Info;
      status_message += "Opened";
      break;
    case ucv::media::CameraStatus::kClosed:
      log_level = ucv::LogLevel::Warn;
      status_message += "Closed";
      break;
    case ucv::media::CameraStatus::kCapturing:
      log_level = ucv::LogLevel::Info;
      status_message += "Capturing";
      break;
    case ucv::media::CameraStatus::kOffline:
      log_level = ucv::LogLevel::Warn;
      status_message += "Offline";
      break;
    case ucv::media::CameraStatus::kNotFound:
      log_level = ucv::LogLevel::Warn;
      status_message += "Not found, please enumerate devices";
      break;
    case ucv::media::CameraStatus::kInvalid:
      log_level = ucv::LogLevel::Warn;
      status_message += "Device is invalid";
      break;
    default:
      break;
  }
  ucv::cameraLogger.log(log_level, "Camera status", status_message.c_str());
}

int main() {
  ChannelManager::GetInstance().Init();

  CameraApiStatus status = CameraApiStatus::Failure;

  std::unique_ptr<CameraCapture> cc = std::make_unique<CameraCapture>("207000156086");
  PrintCameraStatus(cc);

  auto device_list = EnumerateDevices();
  cout << "-------------" << endl;
  cout << "Device list" << endl;
  cout << "-------------" << endl;
  for (const auto& device : device_list) {
    cout << device->ToString() << endl;
  }
  cout << "-------------" << endl;

  if (!device_list.empty()) {
   /* cc = std::make_unique<CameraCapture>(device_list[0]->sn);*/
    PrintCameraStatus(cc);
    status = cc->Connect("");
    bool is_enabled = false;
    cc->IsSensorEnabled(SensorType::kDepth, is_enabled);
    cc->IsSensorEnabled(SensorType::kIrLeft, is_enabled);
    cc->IsSensorEnabled(SensorType::kIrRigth, is_enabled);
    cc->IsSensorEnabled(SensorType::kRgb, is_enabled);

    ucv::cameraLogger.log(status == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error, "Connect",
                          CameraApiStatusToMessage(status).c_str());
    PrintCameraStatus(cc);
  }

  if (status == CameraApiStatus::Success) {
    status = cc->SetSensorEnabled(SensorType::kDepth, true);
    status = cc->SetSensorEnabled(SensorType::kIrLeft, true);
    cc->SetUndistortionEnabled(SensorType::kDepth, true);
    ucv::cameraLogger.log(status == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error,
                          "Undistort depth", CameraApiStatusToMessage(status).c_str());
  }

  if (status == CameraApiStatus::Success) {
    cc->SetUndistortionEnabled(SensorType::kRgb, true);
    ucv::cameraLogger.log(status == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error,
                          "Undistort RGB", CameraApiStatusToMessage(status).c_str());
  }


  if (status == CameraApiStatus::Success) {
    cc->SetFrameSetCb(ReceiveFrameSetCallback);
  }

  
  bool test_soft_trigger = true;
  if (test_soft_trigger) {
    // 切换为软触发
    if (status == CameraApiStatus::Success) {
      status = cc->StopCapture();
    }
    // if (status == CameraApiStatus::Success) {
    //   status = cc->ClearFrameCache();
    // }
    cout << "Please enter to set trigger mode to 22" << endl;
    cin.get();
    if (status == CameraApiStatus::Success) {
      status = cc->SetTriggerMode(TriggerMode::kTriggerMode22);
    }
    ucv::cameraLogger.log(status == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error,
                          "Set trigger mode to 22", CameraApiStatusToMessage(status).c_str());
   /* if (status == CameraApiStatus::Success) {
      status = cc->SetFramePerTrigger(1);
    }*/
    
    cout << "Please enter to start capture" << endl;
    cin.get();
    if (status == CameraApiStatus::Success) {
      status = cc->StartCapture();
    }
    PrintCameraStatus(cc);

    if (status == CameraApiStatus::Success) {
      for (int i = 0; i < 3; ++i) {
        cout << ">>>>>>>>>> Please enter key to fire soft trigger" << endl;
        cin.get();
        /* std::this_thread::sleep_for(std::chrono::milliseconds(500));*/
        auto status1 = cc->FireSoftwareTrigger();
        ucv::cameraLogger.log(status1 == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error,
                              "<<<<<<<<<<< Fire trigger", CameraApiStatusToMessage(status1).c_str());

        /*  auto status2 = cc->FireSoftwareTrigger();
          ucv::cameraLogger.log(status2 == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error,
                                "<<<<<<<<<<< Fire trigger 2nd", CameraApiStatusToMessage(status2).c_str());*/
        // cc->GetFrameSet(ReceiveFrameSetCallback);
      }
    }

    cout << "Please enter key to stop capture" << endl;
    cin.get();
    status = cc->StopCapture();
    ucv::cameraLogger.log(status == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error, "",
                          "StopCapture");
    PrintCameraStatus(cc);
  }

  status = cc->Disconnect();

  ucv::cameraLogger.log(status == CameraApiStatus::Success ? ucv::LogLevel::Info : ucv::LogLevel::Error, "",
                        "Disconnect");

  PrintCameraStatus(cc);

  ChannelManager::GetInstance().Release();
  cout << "Please enter key to exit" << endl;
  cin.get();  // 等待回车键

  return 0;
}
