﻿#pragma once
#include "VisionTK/Camera.h"
#include "ucv/cameraapi/cameraFeature.h"
#include "ucv/cameraapi/cameraFeatureValue.h"
#include "ucv/cameraapi/dataModel.h"
#include "ucv/cameraapi/frameInfo.h"

#include <rapidjson/document.h>
namespace ucv {
namespace media {
/**
 * @brief VisionTK::Status 转换为 CameraApiStatus.
 *
 * \param vtk_status VisionTK::Status
 * \return CameraApiStatus
 */
CameraApiStatus VtkStatusToCameraApiStatus(VisionTK::Status vtk_status);

/**
 * @brief API 层的 trigger mode 转换为 VisionTK 中的 trigger mode.
 *
 * \param trigger_mode
 * \return
 */
VisionTK::TriggerMode TriggerModeToVtkTriggerMode(TriggerMode trigger_mode);

/**
 * @brief Convert VisionTK status to json response value. First convert to CameraApiStatus, then to int32.
 * \param vtk_status VisionTK status
 * \return int32 value to add to json response
 */
int32_t VtkStatusToResponseValue(VisionTK::Status vtk_status);

/**
 * @brief VisionTK 中的 frame flag 转换为 json response 的值（对应 CameraApiStatus 中的 SensorType 值）.
 *
 * \param frame_flag
 * \return
 */
int32_t VtkFrameFlagToResponseValue(VisionTK::FrameFlags frame_flag);

ucv::media::SensorType VtkFrameFlagToMediaSensorType(VisionTK::FrameFlags frame_flag);

/**
 * @brief 获取 cam 中的 sensor.
 *
 * \param cam
 * \param sensor_type
 * \return
 */
VisionTK::Sensor GetSensor(const VisionTK::Camera& cam, SensorType sensor_type);

ucv::media::ImageMode VtkImageModeToMediaImageMode(VisionTK::ImageMode vtk_mode);

VisionTK::ImageMode MediaImageModeToVtkImageMode(ucv::media::ImageMode mode);

ucv::media::CalibInfo VtkCalibInfoToMedia(VisionTK::CalibrationInfo vtk_calib_info);

CameraFeatureAccessMode VtkAccessModeToMedia(VisionTK::AccessMode vtk_access_mode);
int32_t VtkAccessModeToResponseValue(VisionTK::AccessMode vtk_access_mode);

void PrintJson(const rapidjson::Value& json_value);
}  // namespace media
}  // namespace ucv