﻿#include "ucv/base/array.h"
#include "ucv/base/interop/csExport.h"
#include "ucv/base/interop/csInteropTypes.h"
#include "ucv/base/memory/refPtr.h"
#include "ucv/base/object.h"
#include "ucv/base/refPtr.h"
#include "ucv/cameraapi/dataModel.h"

#include <iostream>
#include <string>
#include <vector>

using ucv::Array;
using ucv::RefPtr;

namespace ucv {
namespace media {
class CsDeviceInfoArrayWrapper : public ucv::Object {
  UCV_DECLARE_CLASS(CsDeviceInfoArrayWrapper, Object);

 public:
  CsDeviceInfoArrayWrapper(const Array<RefPtr<DeviceInfo>>& deviceInfos);

  int GetCount() const;

  RefPtr<DeviceInfo> GetDeviceInfo(int index) const;

 private:
  Array<RefPtr<DeviceInfo>> m_deviceInfos;
};
}  // namespace media
}  // namespace ucv