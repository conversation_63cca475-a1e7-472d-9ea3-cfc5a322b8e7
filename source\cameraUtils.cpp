﻿#include "ucv/cameraapi/cameraUtils.h"

#include "deviceInfoManager.h"
#include "generalSignal.h"
#include "ucv/cameraapi/cameraApiConfig.h"
#include "ucv/cameraapi/channelManager.h"
#include "ucv/cameraapi/dataModel.h"
#include "ucv/cameraapi/jsonConstants.h"
#include "ucv/rpc/methodChannelServer.h"
#include "ucv/rpc/methodResultFunctions.h"
#include "ucv/cameraapi/cameraApiUtils.h"
#include "ucv/cameraapi/cameraLogging.h"

#include <rapidjson/document.h>
#include <rapidjson/stringbuffer.h>
#include <rapidjson/writer.h>
#include <rapidjson/prettywriter.h>
#include <string>
#include <thread>
#include <fstream>

namespace ucv {
namespace media {

using std::vector;
using DeviceInfoPtrList = ucv::Array<ucv::RefPtr<DeviceInfo>>;

//struct EnumerateContext {
// public:
//  DeviceInfoPtrList devices;
//};

DeviceInfoPtrList EnumerateDevices() {
  if (CameraApiConfig::direct_call) {
    DeviceInfoPtrList ret_val;
    auto vtk_device_infos = DeviceInfoManager::GetInstance().EnumerateDevices();
    for (const auto& vtk_info : vtk_device_infos) {
      RefPtr<DeviceInfo> refp_dev_info = MakeRefCounted<DeviceInfo>();
      ret_val.push_back(refp_dev_info);
      refp_dev_info->interface_type = static_cast<InterfaceType>(vtk_info.GetInterfaceType());
      auto vtk_net_info = vtk_info.GetNetworkInfo();
      auto vtk_usb_info = vtk_info.GetUsbInfo();
      switch (refp_dev_info->interface_type) {
        case InterfaceType::Network:
          refp_dev_info->network_info.mac = vtk_net_info.mac;
          refp_dev_info->network_info.ip = vtk_net_info.ip;
          refp_dev_info->network_info.netmask= vtk_net_info.netmask;
          refp_dev_info->network_info.gateway= vtk_net_info.gateway;
          refp_dev_info->network_info.broadcast= vtk_net_info.broadcast;
          break;
        case InterfaceType::USB:
          refp_dev_info->usb_info.address = vtk_usb_info.address;
          refp_dev_info->usb_info.bus= vtk_usb_info.bus;
          break;
      }
      refp_dev_info->sn = vtk_info.GetDeviceSN();
      refp_dev_info->name = vtk_info.GetDeviceName();
      refp_dev_info->model_name = vtk_info.GetModelName();
      refp_dev_info->vendor_name = vtk_info.GetVendorName();
      refp_dev_info->firmware_version = vtk_info.GetFirmwareVersion();
      refp_dev_info->config_version = vtk_info.GetConfigVersion();
      refp_dev_info->status_code = vtk_info.GetStatusCode();
      refp_dev_info->interface_IP = vtk_info.GetInterfaceIP();
      refp_dev_info->is_opened_by_service = false;
    }
    return ret_val;
  }

  auto signal = std::make_shared<GeneralSignal<DeviceInfoPtrList>>();

  // 定义 callback
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* success_value) {
        DeviceInfoPtrList device_info_list;

        if (success_value->IsArray()) {
          for (const auto& item : success_value->GetArray()) {
            RefPtr<DeviceInfo> dev_ptr = MakeRefCounted<DeviceInfo>();
            device_info_list.push_back(dev_ptr);

            dev_ptr->interface_type = static_cast<InterfaceType>(item[kInterfaceType].GetInt());

            if (dev_ptr->interface_type == InterfaceType::USB) {
              const rapidjson::Value& usb_info = item[kUsbInfo];
              dev_ptr->usb_info.bus = usb_info[kBus].GetInt();
              dev_ptr->usb_info.address = usb_info[kAddress].GetInt();
            } else if (dev_ptr->interface_type == InterfaceType::Network) {
              const rapidjson::Value& network_info = item[kNetworkInfo];
              dev_ptr->network_info.broadcast = network_info[kBroadcast].GetString();
              dev_ptr->network_info.gateway = network_info[kGateway].GetString();
              dev_ptr->network_info.ip = network_info[kIp].GetString();
              dev_ptr->network_info.mac = network_info[kMac].GetString();
              dev_ptr->network_info.netmask = network_info[kNetmask].GetString();
            }
            dev_ptr->sn = item[kSn].GetString();
            dev_ptr->name = item[kName].GetString();
            dev_ptr->model_name = item[kModelName].GetString();
            dev_ptr->vendor_name = item[kVendorName].GetString();
            dev_ptr->firmware_version = item[kFirmwareVersion].GetString();
            dev_ptr->config_version = item[kConfigVersion].GetString();
            dev_ptr->status_code = item[kStatusCode].GetInt();
            dev_ptr->is_opened_by_service = item[kIsOpenedByService].GetBool();
            dev_ptr->interface_IP = item[kInterfaceIP].GetString();
          }
        }
        signal->produce(device_info_list);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kEnumerateDevices, std::move(args),
                                                             std::move(result_handler));
  // 等待 callback 结束
  bool is_data_valid = false;
  return signal->consume(is_data_valid, std::chrono::milliseconds(10000));
}

CameraApiStatus SaveFeaturesToFile(std::string file_path, ucv::Array<RefPtr<CameraFeature>> feature_list) {
  rapidjson::Document json_doc;
  json_doc.SetObject();

  rapidjson::Value json_feat_array(rapidjson::kArrayType);
  auto& allocator = json_doc.GetAllocator();
  for (const auto& feat : feature_list) {
    rapidjson::Value json_feat = feat->SerializeToJsonObj(allocator);
    json_feat_array.PushBack(json_feat.Move(), allocator);
  }
  json_doc.AddMember(kFeatureList, json_feat_array.Move(), allocator);

  rapidjson::StringBuffer buffer;
  rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
  json_doc.Accept(writer);

  std::ofstream outFile(file_path);
  if (outFile.is_open()) {
    outFile << buffer.GetString();
    outFile.close();
    return CameraApiStatus::Success;
  }
  return CameraApiStatus::Failure;
}

CameraApiStatus LoadFeaturesFromFile(std::string file_path,
                                                   ucv::Array<RefPtr<CameraFeature>>& feat_list) {
  feat_list.clear();

  std::ifstream ifs(file_path);
  if (!ifs.is_open()) {
    return CameraApiStatus::Failure;
  }

  std::stringstream buffer;
  buffer << ifs.rdbuf();

  rapidjson::Document json_doc;
  json_doc.Parse(buffer.str().c_str());
  if (json_doc.HasParseError()) {
    return CameraApiStatus::Failure;
  }

  auto json_feat_array = json_doc[kFeatureList].GetArray();
  for (const auto& json_feat : json_feat_array) {
    RefPtr<CameraFeature> feat = CameraFeature::DeserializeFromJson(json_feat);
    feat_list.push_back(feat);
  }
  return CameraApiStatus::Success;
}
}  // namespace media
}  // namespace ucv
