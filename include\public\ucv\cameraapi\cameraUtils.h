﻿#pragma once

#include "cameraCoreExport.h"
#include "dataModel.h"
#include "ucv/base/array.h"
#include "ucv/base/memory/refPtr.h"
#include "ucv/base/object.h"

#include <functional>
#include <iostream>
#include <string>
#include <vector>
#include "ucv/cameraapi/cameraFeature.h"
#include "ucv/cameraapi/dataModel.h"

namespace ucv {
namespace media {
/**
 * @brief Enumerate all devices that can be found.
 */
CAMERA_CORE_API ucv::Array<ucv::RefPtr<DeviceInfo>> EnumerateDevices();

CAMERA_CORE_API CameraApiStatus SaveFeaturesToFile(std::string file_path, ucv::Array<RefPtr<CameraFeature>>);

CAMERA_CORE_API CameraApiStatus LoadFeaturesFromFile(std::string file_path, ucv::Array<RefPtr<CameraFeature>>& feat_list);

}  // namespace media
}  // namespace ucv