﻿#pragma once

namespace ucv {
namespace media {

const char kCameraServiceName[] = "camera_service";

// IPC/RPC methods
const char kEnumerateDevices[] = "EnumerateDevices";
const char kGetCameraStatus[] = "GetCameraStatus";
const char kConnect[] = "Connect";
const char kDisconnect[] = "Disconnect";
const char kSetSensorEnabled[] = "SetSensorEnabled";
const char kIsSensorEnabled[] = "IsSensorEnabled";
const char kStartCapture[] = "StartCapture";
const char kStopCapture[] = "StopCapture";
const char kClearFrameCache[] = "ClearFrameCache";
const char kGetFrameSet[] = "GetFrameSet";
const char kSetTriggerMode[] = "SetTriggerMode";
const char kSetFramePerTrigger[] = "SetFramePerTrigger";
const char kFireSoftwareTrigger[] = "FireSoftwareTrigger";
const char kReleaseImageSharedMemory[] = "ReleaseImageSharedMemory";
const char kGetImageModes[] = "GetImageModes";
const char kGetCurrentImageMode[] = "GetCurrentImageMode";
const char kSetImageMode[] = "SetImageMode";
const char kGetCalibInfo[] = "GetCalibInfo";
const char kReceiveFrameSetLoop[] = "ReceiveFrameSetLoop";
const char kSetUndistortionEnabled[] = "SetUndistortionEnabled";
const char kGetFeatures[] = "GetFeatures";
const char kGetFeatureValue[] = "GetFeatureValue";
const char kSetFeature[] = "SetFeature";
const char kHasSensor[] = "hasSensor";
const char kSensorTypes[] = "sensorTypes";
const char kIsForceNetDeviceIP[] = "IsForceNetDeviceIP";

// JSON keys
const char kStatus[] = "status";
const char kCameraStatus[] = "cameraStatus";
const char kSn[] = "sn";
const char kSessionKey[] = "sessionKey";
const char kName[] = "name";
const char kId[] = "id";
const char kModelName[] = "modelName";
const char kVendorName[] = "vendorName";
const char kFirmwareVersion[] = "firmwareVersion";
const char kConfigVersion[] = "configVersion";
const char kStatusCode[] = "statusCode";
const char kIsOpenedByService[] = "isOpenedByService";
const char kInterfaceType[] = "interfaceType";
const char kBus[] = "bus";
const char kAddress[] = "address";
const char kUsbInfo[] = "usbInfo";
const char kMac[] = "mac";
const char kIp[] = "ip";
const char kNetmask[] = "netmask";
const char kGateway[] = "gateway";
const char kBroadcast[] = "broadcast";
const char kNetworkInfo[] = "networkInfo";
const char kFrameInfoArray[] = "frameInfoArray";
const char kTimestamp[] = "timestamp";
const char kFrameIndex[] = "frameIndex";
const char kBufferSize[] = "bufferSize";
const char kWidth[] = "width";
const char kHeight[] = "height";
const char kData[] = "data";
const char kPixelFormat[] = "pixelFormat";
const char kBufferOffset[] = "bufferOffset";
const char kSensorType[] = "sensorType";
const char kEnableRequested[] = "enableRequested";
const char kTriggerMode[] = "triggerMode";
const char kFramePerTrigger[] = "framePerTrigger";
const char kSharedMemoryName[] = "sharedMemoryName";
const char kImageModes[] = "imageModes";
const char kIntrinsic[] = "intrinsic";
const char kExtrinsic[] = "extrinsic";
const char kDistortion[] = "distortion";
const char kEnabled[] = "enabled";
const char kScaleUnit[] = "scaleUnit";
const char kGroupName[] = "groupName";
const char kFeatureGroupList[] = "featureGroupList";
const char kFeatureList[] = "featureList";
const char kDevice[] = "device";
const char kValue[] = "value";
const char kValueType[] = "valueType";
const char kAccessMode[] = "accessMode";
const char kCanWriteOnStreaming[] = "canWriteOnStreaming";
const char kDataType[] = "dataType";
const char kContent[] = "content";
const char kType[] = "type";
const char kFeatureValue[] = "featureValue";
const char kFeatureId[] = "featureId";
const char kExist[] = "exist";
const char kInterfaceIP[] = "interfaceIP";
const char kMacAddress[] = "mac";
const char kIP[] = "ip";
const char kMask[] = "mask";




}  // namespace media
}  // namespace ucv