﻿#pragma once
#include "ucv/base/array.h"
#include "ucv/base/refPtr.h"
#include "ucv/cameraapi/dataModel.h"

#include <rapidjson/document.h>
#include <unordered_map>

namespace ucv {
namespace media {
class CameraFeatureValue : public Object {
  UCV_DECLARE_CLASS(CameraFeatureValue, Object);
  // UCV_DECLARE_ABSTRACT_CLASS(CameraFeatureValue, Object);

 public:
  /**
   * @brief 从 JSON 对象中反序列化 FeatureValue.
   */
  static RefPtr<CameraFeatureValue> DeserializeFromJson(const rapidjson::Value& json);

 public:
  virtual ~CameraFeatureValue() = default;
  virtual CameraValueType GetValueType() const { return CameraValueType::kUndefined; }

  virtual bool GetBool() const { return false; }
  virtual int8_t GetInt8() const { return 0; }
  virtual uint8_t GetUInt8() const { return 0; }
  virtual int16_t GetInt16() const { return 0; }
  virtual uint16_t GetUInt16() const { return 0; }
  virtual int32_t GetInt32() const { return 0; }
  virtual uint32_t GetUInt32() const { return 0; }
  virtual int64_t GetInt64() const { return 0; }
  virtual uint64_t GetUInt64() const { return 0; }
  virtual float GetFloat() const { return 0.0f; }
  virtual double GetDouble() const { return 0.0; }
  virtual std::string GetString() const { return ""; }

  virtual Array<RefPtr<CameraFeatureValue>> GetArray() const { return Array<RefPtr<CameraFeatureValue>>(); }
  virtual std::unordered_map<std::string, RefPtr<CameraFeatureValue>> GetStruct() const {
    return std::unordered_map<std::string, RefPtr<CameraFeatureValue>>();
  }
  bool CanHandleValueType();

  /**
   * @brief 将 FeatureValue 序列化为 JSON 对象.
   */
  rapidjson::Value SerializeToJson(rapidjson::Document::AllocatorType& allocator);
};

// 具体类型实现
class BoolValue : public CameraFeatureValue {
  UCV_DECLARE_CLASS(BoolValue, CameraFeatureValue);

 private:
  bool value_;

 public:
  explicit BoolValue(bool value = false) : value_(value) {}
  ~BoolValue() = default;
  CameraValueType GetValueType() const override { return CameraValueType::kBool; }

  bool GetBool() const override { return value_; }

  void SetValue(bool value) { value_ = value; }
  bool GetValue() const { return value_; }
};

class Int8Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(Int8Value, CameraFeatureValue);
  int8_t value_;

 public:
  explicit Int8Value(int8_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kInt8; }
  int8_t GetInt8() const override { return value_; }

  void SetValue(int8_t value) { value_ = value; }
  int8_t GetValue() const { return value_; }
};

class UInt8Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(UInt8Value, CameraFeatureValue);
  uint8_t value_;

 public:
  explicit UInt8Value(uint8_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kUInt8; }

  uint8_t GetUInt8() const override { return value_; }

  void SetValue(uint8_t value) { value_ = value; }
  uint8_t GetValue() const { return value_; }
};

class Int16Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(Int16Value, CameraFeatureValue);
  int16_t value_;

 public:
  explicit Int16Value(int16_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kInt16; }
  int16_t GetInt16() const override { return value_; }

  void SetValue(int16_t value) { value_ = value; }
  int16_t GetValue() const { return value_; }
};

class UInt16Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(UInt16Value, CameraFeatureValue);
  uint16_t value_;

 public:
  explicit UInt16Value(uint16_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kUInt16; }
  uint16_t GetUInt16() const override { return value_; }

  void SetValue(uint16_t value) { value_ = value; }
  uint16_t GetValue() const { return value_; }
};

class Int32Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(Int32Value, CameraFeatureValue);
  int32_t value_;

 public:
  explicit Int32Value(int32_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kInt32; }
  int32_t GetInt32() const override { return value_; }

  void SetValue(int32_t value) { value_ = value; }
  int32_t GetValue() const { return value_; }
};

class UInt32Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(UInt32Value, CameraFeatureValue);
  uint32_t value_;

 public:
  explicit UInt32Value(uint32_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kUInt32; }
  uint32_t GetUInt32() const override { return value_; }

  void SetValue(uint32_t value) { value_ = value; }
  uint32_t GetValue() const { return value_; }
};

class Int64Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(Int64Value, CameraFeatureValue);
  int64_t value_;

 public:
  explicit Int64Value(int64_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kInt64; }
  int64_t GetInt64() const override { return value_; }

  void SetValue(int64_t value) { value_ = value; }
  int64_t GetValue() const { return value_; }
};

class UInt64Value : public CameraFeatureValue {
  UCV_DECLARE_CLASS(UInt64Value, CameraFeatureValue);
  uint64_t value_;

 public:
  explicit UInt64Value(uint64_t value = 0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kUInt64; }
  uint64_t GetUInt64() const override { return value_; }

  void SetValue(uint64_t value) { value_ = value; }
  uint64_t GetValue() const { return value_; }
};

class FloatValue : public CameraFeatureValue {
  UCV_DECLARE_CLASS(FloatValue, CameraFeatureValue);
  float value_;

 public:
  explicit FloatValue(float value = 0.0f) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kFloat; }
  float GetFloat() const override { return value_; }

  void SetValue(float value) { value_ = value; }
  float GetValue() const { return value_; }
};

class DoubleValue : public CameraFeatureValue {
  UCV_DECLARE_CLASS(DoubleValue, CameraFeatureValue);
  double value_;

 public:
  explicit DoubleValue(double value = 0.0) : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kDouble; }
  double GetDouble() const override { return value_; }

  void SetValue(double value) { value_ = value; }
  double GetValue() const { return value_; }
};

class StringValue : public CameraFeatureValue {
  UCV_DECLARE_CLASS(StringValue, CameraFeatureValue);
  std::string value_;

 public:
  explicit StringValue(const std::string& value = "") : value_(value) {}
  CameraValueType GetValueType() const override { return CameraValueType::kString; }
  std::string GetString() const override { return value_; }

  void SetValue(const std::string& value) { value_ = value; }
  const std::string& GetValue() const { return value_; }
};

class ArrayValue : public CameraFeatureValue {
  UCV_DECLARE_CLASS(ArrayValue, CameraFeatureValue);
  Array<RefPtr<CameraFeatureValue>> elements_;

 public:
  CameraValueType GetValueType() const override { return CameraValueType::kArray; }

  Array<RefPtr<CameraFeatureValue>> GetArray() const override { return elements_; }

  void addElement(RefPtr<CameraFeatureValue> element) { elements_.push_back(element); }
  const Array<RefPtr<CameraFeatureValue>>& getElements() const { return elements_; }
  Array<RefPtr<CameraFeatureValue>>& getElements() { return elements_; }
};

class StructValue : public CameraFeatureValue {
  UCV_DECLARE_CLASS(StructValue, CameraFeatureValue);

  std::unordered_map<std::string, RefPtr<CameraFeatureValue>> members_;

 public:
  CameraValueType GetValueType() const override { return CameraValueType::kStruct; }
  std::unordered_map<std::string, RefPtr<CameraFeatureValue>> GetStruct() const override { return members_; }

  void addMember(const std::string& name, RefPtr<CameraFeatureValue> member) { members_[name] = member; }
  const std::unordered_map<std::string, RefPtr<CameraFeatureValue>>& getMembers() const { return members_; }
  std::unordered_map<std::string, RefPtr<CameraFeatureValue>>& getMembers() { return members_; }
};
}  // namespace media
}  // namespace ucv
