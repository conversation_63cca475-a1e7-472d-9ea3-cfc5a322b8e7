﻿#pragma once

#include "ucv/base/rawBuffer.h"
#include "ucv/datamodel/pixelBuffer.h"

namespace ucv {
namespace media {
class RawPixelBuffer : public PixelBuffer {
  UCV_DECLARE_CLASS(RawPixelBuffer, PixelBuffer);

 public:
  RawPixelBuffer() = default;
  RawPixelBuffer(uint32_t width, uint32_t height, uint32_t stride, PixelFormat format, uint32_t bitsPerPixel,
                 ucv::RawBuffer& buffer);
  const uint8_t* data() const override;
  Parcel& Serialize(Parcel&) override;
  Parcel& Deserialize(Parcel&) override;

 private:
  ucv::RawBuffer data_buffer_;
};
}  // namespace media
}  // namespace ucv
