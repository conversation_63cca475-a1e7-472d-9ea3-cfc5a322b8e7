﻿#pragma once

#include "camera_method_handler.h"

#include "VisionTK/DeviceManager.h"
#include "VisionTK/DictionaryImpl.h"
#include "VisionTK/ForceNetDeviceIP.h"
#include "ucv/base/serialization/sharedMemory.h"
#include "ucv/cameraapi/cameraApiUtils.h"
#include "ucv/cameraapi/cameraLogging.h"
#include "ucv/cameraapi/dataModel.h"
#include "ucv/cameraapi/jsonConstants.h"

#define SEND_METHOD_RESULT_AND_RETURN(result, json_response, status) \
  do {                                                               \
    SendMethodResult(result, json_response, status);                 \
    return;                                                          \
  } while (0)

namespace ucv {
namespace media {
CameraMethodHandler::CameraMethodHandler() : cam_pool_(CameraPool::GetInstance()) {}

void CameraMethodHandler::HandleMethodCall(const MethodCall<rapidjson::Document>& call,
                                           std::shared_ptr<MethodResult<rapidjson::Document>> result,
                                           std::weak_ptr<MethodChannel<rapidjson::Document>> current_channel) {
  const std::string& method = call.method_name();
  const rapidjson::Value* params = call.arguments();
  if (method == kEnumerateDevices) {
    Handle_EnumerateDevices(call, result);
  }
  if (method == kGetCameraStatus) {
    Handle_GetCameraStatus(call, result);
  }
  if (method == kConnect) {
    Handle_Connect(call, result);
  }
  if (method == kDisconnect) {
    Handle_Disconnect(call, result);
  }
  if (method == kSetSensorEnabled) {
    Handle_SetSensorEnabled(call, result);
  }
  if (method == kIsSensorEnabled) {
    Handle_IsSensorEnabled(call, result);
  }
  if (method == kStartCapture) {
    Handle_StartCapture(call, result);
  }
  if (method == kStopCapture) {
    Handle_StopCapture(call, result);
  }

  if (method == kIsForceNetDeviceIP) {
    Handle_IsForceNetDeviceIP(call, result);
  }

  /*if (method == kClearFrameCache) {
    Handle_ClearFrameCache(call, result);
  }
  if (method == kGetFrameSet) {
    Handle_GetFrameSet(call, result);
  }*/
  if (method == kSetTriggerMode) {
    Handle_SetTriggerMode(call, result);
  }
  if (method == kSetFramePerTrigger) {
    Handle_SetFramePerTrigger(call, result);
  }
  if (method == kFireSoftwareTrigger) {
    Handle_FireSoftTrigger(call, result);
  }
  if (method == kReleaseImageSharedMemory) {
    Handle_ReleaseImageSharedMemory(call, result);
  }
  if (method == kGetImageModes) {
    Handle_GetImageModes(call, result);
  }
  if (method == kGetCurrentImageMode) {
    Handle_GetCurrentImageMode(call, result);
  }
  if (method == kSetImageMode) {
    Handle_SetImageMode(call, result);
  }
  if (method == kGetCalibInfo) {
    Handle_GetCalibInfo(call, result);
  }
  if (method == kReceiveFrameSetLoop) {
    Handle_ReceiveFrameSetLoop(call, result, current_channel);
  }
  if (method == kSetUndistortionEnabled) {
    Handle_SetUndistortEnabled(call, result);
  }
  if (method == kGetFeatures) {
    Handle_GetFeatures(call, result);
  }
  if (method == kGetFeatureValue) {
    Handle_GetFeatureValue(call, result);
  }
  if (method == kSetFeature) {
    Handle_SetFeature(call, result);
  }
  if (method == kHasSensor) {
    Handle_HasSensor(call, result);
  }
}

void CameraMethodHandler::Handle_EnumerateDevices(const MethodCall<rapidjson::Document>& call,
                                                  std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  auto device_manager = VisionTK::DeviceManager::Instance();
  auto device_info_list = device_manager->EnumerateDevices();
  CameraPool::GetInstance().cached_deviceinfo_list = device_info_list;

  rapidjson::Document json_response;
  json_response.SetArray();
  rapidjson::Document::AllocatorType& allocator = json_response.GetAllocator();

  for (VisionTK::DeviceInfo& devInfo : device_info_list) {
    // Quick and dirty implementation
    std::string sn = devInfo.GetDeviceSN();
    RefPtr<CameraWrapper> cam_wrapper_ptr;
    if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
      cam_wrapper_ptr = MakeRefCounted<CameraWrapper>();
      cam_wrapper_ptr->cam = VisionTK::Camera::ForDevice(devInfo);
      cam_pool_.map_cameras_[sn] = cam_wrapper_ptr;
    } else {
      cam_wrapper_ptr = cam_pool_.map_cameras_[sn];
    }

    rapidjson::Value json_device_info(rapidjson::kObjectType);

    // Add basic device info
    json_device_info.AddMember(kSn, rapidjson::Value(devInfo.GetDeviceSN().c_str(), allocator).Move(), allocator);
    json_device_info.AddMember(kName, rapidjson::Value(devInfo.GetDeviceName().c_str(), allocator).Move(), allocator);
    json_device_info.AddMember(kModelName, rapidjson::Value(devInfo.GetModelName().c_str(), allocator).Move(),
                               allocator);
    json_device_info.AddMember(kVendorName, rapidjson::Value(devInfo.GetVendorName().c_str(), allocator).Move(),
                               allocator);
    json_device_info.AddMember(kFirmwareVersion,
                               rapidjson::Value(devInfo.GetFirmwareVersion().c_str(), allocator).Move(), allocator);
    json_device_info.AddMember(kConfigVersion, rapidjson::Value(devInfo.GetConfigVersion().c_str(), allocator).Move(),
                               allocator);
    json_device_info.AddMember(kStatusCode, devInfo.GetStatusCode(), allocator);
    json_device_info.AddMember(kIsOpenedByService, cam_wrapper_ptr->opened_by_service, allocator);
    json_device_info.AddMember(kInterfaceIP, rapidjson::Value(devInfo.GetInterfaceIP().c_str(), allocator).Move(),
                               allocator);

    if (devInfo.IsUsb()) {
      json_device_info.AddMember(kInterfaceType, 1, allocator);

      auto usb_info = devInfo.GetUsbInfo();
      rapidjson::Value usb_info_obj(rapidjson::kObjectType);
      usb_info_obj.AddMember(kBus, usb_info.bus, allocator);
      usb_info_obj.AddMember(kAddress, usb_info.address, allocator);

      json_device_info.AddMember(kUsbInfo, usb_info_obj, allocator);
    } else if (devInfo.IsNetwork()) {
      json_device_info.AddMember(kInterfaceType, 2, allocator);

      auto net_info = devInfo.GetNetworkInfo();
      rapidjson::Value net_info_obj(rapidjson::kObjectType);
      net_info_obj.AddMember(kMac, rapidjson::Value(net_info.mac.c_str(), allocator).Move(), allocator);
      net_info_obj.AddMember(kIp, rapidjson::Value(net_info.ip.c_str(), allocator).Move(), allocator);
      net_info_obj.AddMember(kNetmask, rapidjson::Value(net_info.netmask.c_str(), allocator).Move(), allocator);
      net_info_obj.AddMember(kGateway, rapidjson::Value(net_info.gateway.c_str(), allocator).Move(), allocator);
      net_info_obj.AddMember(kBroadcast, rapidjson::Value(net_info.broadcast.c_str(), allocator).Move(), allocator);

      json_device_info.AddMember(kNetworkInfo, net_info_obj, allocator);
    } else {
      json_device_info.AddMember(kInterfaceType, 0, allocator);
    }

    json_response.PushBack(json_device_info, allocator);
  }

  result->Success(json_response);
}

void CameraMethodHandler::Handle_GetCameraStatus(const MethodCall<rapidjson::Document>& call,
                                                 std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  // json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());
  json_response.AddMember(kCameraStatus, static_cast<int32_t>(CameraStatus::kInvalid), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    json_response[kCameraStatus] = static_cast<int32_t>(CameraStatus::kNotFound);
    result->Success(json_response);
    return;
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  VisionTK::Status vtk_status = VisionTK::Status::Error;
  if (cam.IsCapturing(&vtk_status) && vtk_status == VisionTK::Status::OK) {
    json_response[kCameraStatus] = static_cast<int32_t>(CameraStatus::kCapturing);
  } else if (cam.IsOfflined(&vtk_status) && vtk_status == VisionTK::Status::OK) {
    json_response[kCameraStatus] = static_cast<int32_t>(CameraStatus::kOffline);
  } else if (cam.IsOpened(&vtk_status) && vtk_status == VisionTK::Status::OK) {
    json_response[kCameraStatus] = static_cast<int32_t>(CameraStatus::kOpened);
  } else if (!cam.IsOpened(&vtk_status) && vtk_status == VisionTK::Status::OK) {
    json_response[kCameraStatus] = static_cast<int32_t>(CameraStatus::kClosed);
  } else {
    json_response[kCameraStatus] = static_cast<int32_t>(CameraStatus::kInvalid);
  }
  result->Success(json_response);
}

void CameraMethodHandler::Handle_Connect(const MethodCall<rapidjson::Document>& call,
                                         std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  std::string session_key = (*args)[kSessionKey].GetString();

  bool is_opened = false;
  VisionTK::Status vtk_status = VisionTK::Status::OK;

  auto& map_cameras_ = cam_pool_.map_cameras_;
  if (map_cameras_.find(sn) == map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  RefPtr<CameraWrapper> cam_wrapper_ptr = map_cameras_[sn];
  is_opened = cam_wrapper_ptr->cam.IsOpened(&vtk_status);
  // 获取 IsOpened 状态失败
  if (vtk_status != VisionTK::Status::OK) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }
  if (!is_opened) {
    // 打开设备
    vtk_status = cam_wrapper_ptr->cam.OpenDevice();
    // 打开设备失败
    if (vtk_status != VisionTK::Status::OK) {
      SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
    } else {
      cam_wrapper_ptr->opened_by_service = true;
    }
  }

  // 增加相机本身的静态信息（即连接后不会发生的变化的信息）
  const auto& cam = cam_wrapper_ptr->cam;
  rapidjson::Value sensor_array(rapidjson::kArrayType);
  if (cam.HasDepthSensor()) {
    sensor_array.PushBack(static_cast<int32_t>(SensorType::kDepth), allocator);
  }
  if (cam.HasColorSensor()) {
    sensor_array.PushBack(static_cast<int32_t>(SensorType::kRgb), allocator);
  }
  if (cam.HasInfraredLeftSensor()) {
    sensor_array.PushBack(static_cast<int32_t>(SensorType::kIrLeft), allocator);
  }
  if (cam.HasInfraredRightSensor()) {
    sensor_array.PushBack(static_cast<int32_t>(SensorType::kIrRigth), allocator);
  }
  json_response.AddMember(kSensorTypes, sensor_array.Move(), allocator);

  if (cam_wrapper_ptr->client_ss_map.find(session_key) == cam_wrapper_ptr->client_ss_map.end()) {
    cam_wrapper_ptr->client_ss_map[session_key] = MakeRefCounted<ClientSession>(session_key);
  }

  if (!cam_wrapper_ptr->frameset_stream.Invalid()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
  }

  StdFuncVtkFrameSetRefCallback cb = [this, sn](VisionTK::FrameSet frame_set) {
    if (frame_set.Invalid()) {
      UCV_CAMERA_LOGW("service frame cb", "Frame is invalid.");
      return;
    }
    if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
      return;
    }
    auto& client_ss_map = cam_pool_.map_cameras_[sn]->client_ss_map;
    for (auto it = client_ss_map.begin(); it != client_ss_map.end(); ++it) {
      RefPtr<ClientSession> refp_client_ss = it->second;
      if (!refp_client_ss->need_frame_set) {
        continue;
      }
      if (refp_client_ss->shared_memory_pool.size() >= 3) {
        continue;
      }

      if (!refp_client_ss->client_result->IsValid()) {
        UCV_CAMERA_LOGW("service frame cb", "client %s session offline.", it->first.c_str());
        continue;
      }

      rapidjson::Document json_response;
      json_response.SetObject();
      json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

      VisionTK::Status vtk_status = VisionTK::Status::OK;
      // GetFrameFlags 失败
      auto frame_flags = frame_set.GetFrameFlags(&vtk_status);
      if (vtk_status != VisionTK::Status::OK) {
        SEND_METHOD_RESULT_AND_RETURN(refp_client_ss->client_result, json_response, vtk_status);
      }

      VisionTK::FrameFlags frame_flags_wanted[] = {
          VisionTK::FrameFlags::Color,
          VisionTK::FrameFlags::Depth,
          VisionTK::FrameFlags::InfraredLeft,
          VisionTK::FrameFlags::InfraredRight,
      };
      std::vector<VisionTK::FrameFlags> valid_frame_flags;
      uint64_t total_buffer_size = 0;
      for (const auto frame_flag : frame_flags_wanted) {
        if ((frame_flags & frame_flag) == frame_flag) {
          auto frame = frame_set.GetFrame(frame_flag, &vtk_status);
          if (vtk_status != VisionTK::Status::OK) {
            SEND_METHOD_RESULT_AND_RETURN(refp_client_ss->client_result, json_response, vtk_status);
          }
          valid_frame_flags.push_back(frame_flag);
          auto buffer_size = frame.GetFrameInfo().bufferSize;
          total_buffer_size += buffer_size;
        }
      }
      auto shared_mem = SharedMemory::Create("", total_buffer_size);
      // 创建共享内存失败
      if (!shared_mem) {
        SEND_METHOD_RESULT_AND_RETURN(refp_client_ss->client_result, json_response, CameraApiStatus::OutOfMemory);
      }
      refp_client_ss->shared_memory_pool[shared_mem->GetName()] = shared_mem;

      uint64_t buffer_offset = 0;
      auto& allocator = json_response.GetAllocator();
      // 创建数组
      rapidjson::Value json_frame_array(rapidjson::kArrayType);
      for (const auto frame_flag : valid_frame_flags) {
        auto frame = frame_set.GetFrame(frame_flag, &vtk_status);
        if (vtk_status != VisionTK::Status::OK) {
          SEND_METHOD_RESULT_AND_RETURN(refp_client_ss->client_result, json_response, vtk_status);
        }
        VisionTK::FrameInfo frame_info = frame.GetFrameInfo();
        // 添加 frame info 到 array
        rapidjson::Value json_frame_info(rapidjson::kObjectType);
        json_frame_info.AddMember(kSensorType, VtkFrameFlagToResponseValue(frame_flag), allocator);
        json_frame_info.AddMember(kTimestamp, frame_info.timeStamp, allocator);
        json_frame_info.AddMember(kFrameIndex, frame_info.frameIndex, allocator);
        json_frame_info.AddMember(kBufferSize, frame_info.bufferSize, allocator);
        json_frame_info.AddMember(kWidth, frame_info.width, allocator);
        json_frame_info.AddMember(kHeight, frame_info.height, allocator);
        json_frame_info.AddMember(kPixelFormat, static_cast<int32_t>(frame_info.pixelFormat), allocator);
        json_frame_info.AddMember(kBufferOffset, buffer_offset, allocator);

        auto meta_data = frame.GetFrameMetadata();

        if (frame_flag == VisionTK::FrameFlags::Depth) {
          float depth_scale = meta_data.GetDepthScale();
          json_response.AddMember(kScaleUnit, depth_scale, json_response.GetAllocator());
        }

        VisionTK::CalibrationInfo calib_info = meta_data.GetCalirationInfo();
        float intrinsic_scale_ratio = 1.0 * frame_info.width / calib_info.GetCameraIntrinsic().width;
        AddCalibInfoToJsonObject(json_frame_info, json_response, calib_info, intrinsic_scale_ratio);

        // 将对象添加到数组
        json_frame_array.PushBack(json_frame_info, allocator);
        // 写入该 frame 数据到共享内存
        shared_mem->WriteBytes(frame.GetData(), frame_info.bufferSize, buffer_offset);
        buffer_offset += frame_info.bufferSize;
      }
      // 将数组添加到response中的frameInfoArray字段
      json_response.AddMember(kFrameInfoArray, json_frame_array, allocator);

      // 共享内存数据加入到 JSON response
      shared_mem->AppendToJson(json_response);

      SendMethodResult(refp_client_ss->client_result, json_response, CameraApiStatus::Success);
    }
  };

  map_cameras_[sn]->frameset_stream = FrameSetStream2::New(cam_wrapper_ptr->cam, cb, &vtk_status);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_Disconnect(const MethodCall<rapidjson::Document>& call,
                                            std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam_wrapper_refptr = cam_pool_.map_cameras_[sn];
  cam_wrapper_refptr->client_ss_map.erase(session_key);

  auto& cam = cam_wrapper_refptr->cam;
  VisionTK::Status vtk_status;
  bool is_opened = cam.IsOpened(&vtk_status);
  if (vtk_status == VisionTK::Status::OK && !is_opened) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
  } else {
    if (cam_wrapper_refptr->client_ss_map.empty()) {
      vtk_status = cam.CloseDevice();
      cam_wrapper_refptr->opened_by_service = false;
      SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
    }
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
  }
}

void CameraMethodHandler::Handle_SetSensorEnabled(const MethodCall<rapidjson::Document>& call,
                                                  std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());
  bool enable_requested = (*args)[kEnableRequested].GetBool();
  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  auto sensor = GetSensor(cam, sensor_type);
  VisionTK::Status vtk_status;
  bool is_enabled = sensor.IsEnabled(&vtk_status);
  if (vtk_status == VisionTK::Status::OK && is_enabled == enable_requested) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
  }
  vtk_status = sensor.SetEnabled(enable_requested);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_IsSensorEnabled(const MethodCall<rapidjson::Document>& call,
                                                 std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());
  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  auto sensor = GetSensor(cam, sensor_type);
  VisionTK::Status vtk_status = VisionTK::Status::Error;
  bool is_enabled = sensor.IsEnabled(&vtk_status);
  json_response.AddMember(kEnabled, is_enabled, allocator);

  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_StartCapture(const MethodCall<rapidjson::Document>& call,
                                              std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }
  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  if (cam.IsCapturing()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
  } else {
    auto vtk_status = cam.StartCapture();
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }
}

void CameraMethodHandler::Handle_StopCapture(const MethodCall<rapidjson::Document>& call,
                                             std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  // 取消订阅
  cam_pool_.map_cameras_[sn]->client_ss_map[session_key]->need_frame_set = false;

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  if (!cam.IsCapturing()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
  } else {
    auto vtk_status = cam.StopCapture();
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }
}

/*
void CameraMethodHandler::Handle_ClearFrameCache(const MethodCall<rapidjson::Document>& call,
                                                 std::unique_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }
  auto& stream = cam_pool_.map_cameras_[sn]->frameset_stream;
  auto vtk_status = stream.Clear();
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_GetFrameSet(const MethodCall<rapidjson::Document>& call,
                                             std::unique_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  // std::string session_key = (*args)[kSessionKey].GetString();

  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    json_response[kStatus] = CameraApiStatusToInt32(CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  auto& stream = cam_pool_.map_cameras_[sn]->frameset_stream;
  VisionTK::Status vtk_status;
  int timeout_ms = 1000;
  int time_interval_ms = 100;
  VisionTK::FrameSet frameset;
  for (int i = 0; i < timeout_ms / time_interval_ms; ++i) {
    std::this_thread::sleep_for(std::chrono::milliseconds(time_interval_ms));
    frameset = stream.ReadNext(&vtk_status);
    // ReadNext 成功
    if (vtk_status == VisionTK::Status::OK) {
      // double check
      frameset.GetFrameFlags(&vtk_status);
      if (vtk_status == VisionTK::Status::OK) {
        break;
      }
    }
  }
  if (vtk_status != VisionTK::Status::OK) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }

  // GetFrameFlags 失败
  auto frame_flags = frameset.GetFrameFlags(&vtk_status);
  if (vtk_status != VisionTK::Status::OK) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }

  VisionTK::FrameFlags frame_flags_wanted[] = {
      VisionTK::FrameFlags::Color,
      VisionTK::FrameFlags::Depth,
  };
  std::vector<VisionTK::FrameFlags> valid_frame_flags;
  uint64_t total_buffer_size = 0;
  for (const auto frame_flag : frame_flags_wanted) {
    if ((frame_flags & frame_flag) == frame_flag) {
      auto frame = frameset.GetFrame(frame_flag, &vtk_status);
      if (vtk_status != VisionTK::Status::OK) {
        SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
      }
      valid_frame_flags.push_back(frame_flag);
      auto buffer_size = frame.GetFrameInfo().bufferSize;
      total_buffer_size += buffer_size;
    }
  }
  auto shared_mem = SharedMemory::Create("", total_buffer_size);
  // 创建共享内存失败
  if (!shared_mem) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::OutOfMemory);
  }
  cam_pool_.shared_memory_pool_[shared_mem->GetName()] = shared_mem;

  uint64_t buffer_offset = 0;
  auto& allocator = json_response.GetAllocator();
  // 创建数组
  rapidjson::Value json_frame_array(rapidjson::kArrayType);
  for (const auto frame_flag : valid_frame_flags) {
    auto frame = frameset.GetFrame(frame_flag, &vtk_status);
    if (vtk_status != VisionTK::Status::OK) {
      SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
    }
    VisionTK::FrameInfo frame_info = frame.GetFrameInfo();
    // 添加 frame info 到 array
    rapidjson::Value json_frame_info(rapidjson::kObjectType);
    json_frame_info.AddMember(kSensorType, VtkFrameFlagToResponseValue(frame_flag), allocator);
    json_frame_info.AddMember(kTimestamp, frame_info.timeStamp, allocator);
    json_frame_info.AddMember(kFrameIndex, frame_info.frameIndex, allocator);
    json_frame_info.AddMember(kBufferSize, frame_info.bufferSize, allocator);
    json_frame_info.AddMember(kWidth, frame_info.width, allocator);
    json_frame_info.AddMember(kHeight, frame_info.height, allocator);
    json_frame_info.AddMember(kPixelFormat, static_cast<int32_t>(frame_info.pixelFormat), allocator);
    json_frame_info.AddMember(kBufferOffset, buffer_offset, allocator);

    // 将对象添加到数组
    json_frame_array.PushBack(json_frame_info, allocator);
    // 写入该 frame 数据到共享内存
    shared_mem->WriteBytes(frame.GetData(), frame_info.bufferSize, buffer_offset);
    buffer_offset += frame_info.bufferSize;
  }
  // 将数组添加到response中的frameInfoArray字段
  json_response.AddMember(kFrameInfoArray, json_frame_array, allocator);

  // 共享内存数据加入到 JSON response
  shared_mem->AppendToJson(json_response);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
}
*/

void CameraMethodHandler::Handle_SetTriggerMode(const MethodCall<rapidjson::Document>& call,
                                                std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  TriggerMode trigger_mode = static_cast<TriggerMode>((*args)[kTriggerMode].GetInt());

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }
  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  auto vtk_status = cam.SetTriggerMode(TriggerModeToVtkTriggerMode(trigger_mode));
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_SetFramePerTrigger(const MethodCall<rapidjson::Document>& call,
                                                    std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  int num_frames_per_trigger = (*args)[kFramePerTrigger].GetInt();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }
  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  auto vtk_status = cam.SetFramePerTrigger(num_frames_per_trigger);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_FireSoftTrigger(const MethodCall<rapidjson::Document>& call,
                                                 std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }
  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  auto vtk_status = cam.FireSoftwareTrigger();
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_ReleaseImageSharedMemory(const MethodCall<rapidjson::Document>& call,
                                                          std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string mem_name = (*args)[kSharedMemoryName].GetString();
  std::string sn = (*args)[kSn].GetString();
  std::string session_key = (*args)[kSessionKey].GetString();

  // 将共享内存的 shared_ptr 从池中移除，达到移除共享内存的目的（according to zhaoda）
  size_t num_elems = cam_pool_.map_cameras_[sn]->client_ss_map[session_key]->shared_memory_pool.erase(mem_name);
  if (num_elems == 0) {
    UCV_CAMERA_LOGW("Release shared memory", "key %s not exist", mem_name.c_str());
  }
}

void CameraMethodHandler::Handle_GetImageModes(const MethodCall<rapidjson::Document>& call,
                                               std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());

  rapidjson::Document json_response;
  json_response.SetObject();
  rapidjson::Document::AllocatorType& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;

  VisionTK::Sensor sensor = GetSensor(cam, sensor_type);
  VisionTK::Status vtk_status = VisionTK::Status::Error;
  auto image_modes = sensor.GetImageModes(&vtk_status);
  /* if (vtk_status != VisionTK::Status::OK) {
     SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
     return;
   }*/

  // 创建 json 数组
  rapidjson::Value json_image_mode_array(rapidjson::kArrayType);
  for (const auto& mode : image_modes) {
    rapidjson::Value json_image_mode(rapidjson::kObjectType);

    // Add basic device info
    json_image_mode.AddMember(kWidth, mode.width, allocator);
    json_image_mode.AddMember(kHeight, mode.height, allocator);
    json_image_mode.AddMember(kPixelFormat, static_cast<int32_t>(mode.format), allocator);

    // 将对象添加到数组
    json_image_mode_array.PushBack(json_image_mode, allocator);
  }
  // 将数组添加到response中的frameInfoArray字段
  json_response.AddMember(kImageModes, json_image_mode_array, allocator);
  if (image_modes.size() > 0) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
  } else {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Failure);
  }
}

void CameraMethodHandler::Handle_GetCurrentImageMode(const MethodCall<rapidjson::Document>& call,
                                                     std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());

  rapidjson::Document json_response;
  json_response.SetObject();
  rapidjson::Document::AllocatorType& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;

  VisionTK::Sensor sensor = GetSensor(cam, sensor_type);
  VisionTK::Status vtk_status = VisionTK::Status::Error;
  auto image_mode = sensor.GetImageMode(&vtk_status);
  if (vtk_status != VisionTK::Status::OK) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }

  // Add basic device info
  json_response.AddMember(kWidth, image_mode.width, allocator);
  json_response.AddMember(kHeight, image_mode.height, allocator);
  json_response.AddMember(kPixelFormat, static_cast<int32_t>(image_mode.format), allocator);

  SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
}

void CameraMethodHandler::Handle_SetImageMode(const MethodCall<rapidjson::Document>& call,
                                              std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());
  int32_t width = (*args)[kWidth].GetInt();
  int32_t height = (*args)[kHeight].GetInt();
  VisionTK::PixelFormat pixel_format = static_cast<VisionTK::PixelFormat>((*args)[kPixelFormat].GetInt());

  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  VisionTK::Status vtk_status;
  auto sensor = GetSensor(cam, sensor_type);
  vtk_status = sensor.SetImageMode(VisionTK::ImageMode(width, height, pixel_format));

  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::AddCalibInfoToJsonObject(rapidjson::Value& json_obj, rapidjson::Document& json_doc,
                                                   const VisionTK::CalibrationInfo& calib_info,
                                                   float intrinsic_scale_ratio) {
  auto& allocator = json_doc.GetAllocator();

  VisionTK::Status vtk_status;
  VisionTK::CameraIntrinsicInfo intrinsic = calib_info.GetCameraIntrinsic(&vtk_status);
  VisionTK::CameraExtrinsicInfo extrinsic = calib_info.GetCameraExtrinsic(&vtk_status);
  VisionTK::CameraDistortionInfo distortion = calib_info.GetCameraDistortion(&vtk_status);

  // 添加 intrinsic
  {
    rapidjson::Value intrinsic_obj(rapidjson::kObjectType);

    // 添加分辨率
    intrinsic_obj.AddMember(kWidth, static_cast<int>(intrinsic.width * intrinsic_scale_ratio), allocator);
    intrinsic_obj.AddMember(kHeight, static_cast<int>(intrinsic.height * intrinsic_scale_ratio), allocator);

    // 添加矩阵数据
    rapidjson::Value matrix_array(rapidjson::kArrayType);
    for (int i = 0; i < 8; i++) {
      matrix_array.PushBack(intrinsic.data[i] * intrinsic_scale_ratio, allocator);
    }
    matrix_array.PushBack((float)1.0, allocator);
    intrinsic_obj.AddMember(kData, matrix_array, allocator);

    json_obj.AddMember(kIntrinsic, intrinsic_obj, allocator);
  }

  // 添加 extrinsic
  {
    rapidjson::Value extrinsic_obj(rapidjson::kObjectType);

    rapidjson::Value matrix_array(rapidjson::kArrayType);
    for (int i = 0; i < 16; i++) {
      matrix_array.PushBack(extrinsic.data[i], allocator);
    }
    extrinsic_obj.AddMember(kData, matrix_array, allocator);

    json_obj.AddMember(kExtrinsic, extrinsic_obj, allocator);
  }

  // 添加 distortion
  {
    rapidjson::Value distortion_obj(rapidjson::kObjectType);

    rapidjson::Value coeff_array(rapidjson::kArrayType);
    for (int i = 0; i < 12; i++) {
      coeff_array.PushBack(distortion.data[i], allocator);
    }
    distortion_obj.AddMember(kData, coeff_array, allocator);

    json_obj.AddMember(kDistortion, distortion_obj, allocator);
  }
}

bool CameraMethodHandler::CanHandleVtkValueType(const VisionTK::Value& vtk_value) {
  if (vtk_value.IsArray()) {
    return false;
  }
  if (vtk_value.IsString()) {
    return false;
  }
  if (vtk_value.IsUndefined()) {
    return false;
  }
  return true;
}

bool CameraMethodHandler::CanHandleVtkFeature(const VisionTK::Feature& vtk_feat) {
  VisionTK::Value vtk_value = vtk_feat.GetValue();
  if (!CanHandleVtkValueType(vtk_value)) {
    return false;
  }

  // check name
  std::string name = vtk_feat.GetName();

  if (name.empty()) return false;

  bool name_all_digit = true;
  for (char c : name) {
    if (!std::isdigit(c)) {
      name_all_digit = false;
      break;
    }
  }
  if (name_all_digit) {
    return false;
  }
  return true;
}

void CameraMethodHandler::Handle_GetCalibInfo(const MethodCall<rapidjson::Document>& call,
                                              std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  VisionTK::Sensor sensor = GetSensor(cam, sensor_type);
  VisionTK::Status vtk_status = VisionTK::Status::Error;
  VisionTK::CalibrationInfo calib_info = sensor.GetCalibrationInfo(&vtk_status);
  if (vtk_status != VisionTK::Status::OK) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }

  AddCalibInfoToJsonObject(json_response, json_response, calib_info);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_ReceiveFrameSetLoop(const MethodCall<rapidjson::Document>& call,
                                                     std::shared_ptr<MethodResult<rapidjson::Document>>& result,
                                                     std::weak_ptr<MethodChannel<rapidjson::Document>> current_channel) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  RefPtr<CameraWrapper> cam_wrapper = cam_pool_.map_cameras_[sn];

  // 不会重复设置回调 result
  if (cam_wrapper->client_ss_map[session_key]->need_frame_set) {
    return;
  }

  // To call methods through the weak_ptr:
  if (auto temp_ptr_channel = current_channel.lock()) {
    // The object still exists, safe to use
    temp_ptr_channel->AddPendingResult(result);
  } else {
    // The object has been destroyed
    std::cout << "Object no longer exists" << std::endl;
    return;
  }
  cam_wrapper->client_ss_map[session_key]->client_result = result;
  cam_wrapper->client_ss_map[session_key]->need_frame_set = true;
  // SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
}

void CameraMethodHandler::Handle_SetUndistortEnabled(const MethodCall<rapidjson::Document>& call,
                                                     std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), json_response.GetAllocator());

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());
  bool enable_requested = (*args)[kEnableRequested].GetBool();
  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  auto sensor = GetSensor(cam, sensor_type);
  VisionTK::Status vtk_status;
  vtk_status = sensor.EnableUndistortion(enable_requested);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::ProcessVtkFeatureGroup(RefPtr<CameraWrapper> cam_wrapper,
                                                 std::vector<RefPtr<CameraFeature>>& media_features,
                                                 std::string group_name,
                                                 const std::vector<VisionTK::Feature>& vtk_features) {
  if (vtk_features.empty()) {
    return;
  }

  for (int i = 0; i < vtk_features.size(); ++i) {
    VisionTK::Feature vtk_feat = vtk_features[i];
    if (!CanHandleVtkFeature(vtk_feat)) {
      continue;
    }
    // store feature in service, so we can utilize it to r/w feature
    int feat_id = cam_wrapper->feature_map.size();
    cam_wrapper->feature_map[feat_id] = vtk_feat;
    auto media_feat = VtkFeatureToMedia(vtk_feat, group_name, feat_id);
    media_features.push_back(media_feat);
  }
}

RefPtr<CameraFeature> CameraMethodHandler::VtkFeatureToMedia(VisionTK::Feature vtk_feature, std::string group_name,
                                                             int id) {
  RefPtr<CameraFeature> ret_val = MakeRefCounted<CameraFeature>();
  ret_val->id = id;
  ret_val->group_name = group_name;
  ret_val->name = vtk_feature.GetName();
  ret_val->access_mode = VtkAccessModeToMedia(vtk_feature.GetAccessMode());
  ret_val->can_write_on_streaming = vtk_feature.CanWriteOnStreaming();
  ret_val->refp_value = VtkValueToMedia(vtk_feature.GetValue());

  return ret_val;
}

RefPtr<CameraFeatureValue> CameraMethodHandler::VtkValueToMedia(const VisionTK::Value& vtk_value) {
  if (!CanHandleVtkValueType(vtk_value)) {
    return MakeRefCounted<CameraFeatureValue>();
  }

  // 处理基本类型
  if (vtk_value.IsBool()) {
    return MakeRefCounted<BoolValue>((bool)vtk_value);
  } else if (vtk_value.IsInt8()) {
    return MakeRefCounted<Int8Value>((int8_t)vtk_value);
  } else if (vtk_value.IsUInt8()) {
    return MakeRefCounted<UInt8Value>((uint8_t)vtk_value);
  } else if (vtk_value.IsInt16()) {
    return MakeRefCounted<Int16Value>((int16_t)vtk_value);
  } else if (vtk_value.IsUInt16()) {
    return MakeRefCounted<UInt16Value>((uint16_t)vtk_value);
  } else if (vtk_value.IsInt32()) {
    return MakeRefCounted<Int32Value>((int32_t)vtk_value);
  } else if (vtk_value.IsUInt32()) {
    return MakeRefCounted<UInt32Value>((uint32_t)vtk_value);
  } else if (vtk_value.IsInt64()) {
    return MakeRefCounted<Int64Value>((int64_t)vtk_value);
  } else if (vtk_value.IsUInt64()) {
    return MakeRefCounted<UInt64Value>((uint64_t)vtk_value);
  } else if (vtk_value.IsFloat()) {
    return MakeRefCounted<FloatValue>((float)vtk_value);
  } else if (vtk_value.IsDouble()) {
    return MakeRefCounted<DoubleValue>((double)vtk_value);
  } else if (vtk_value.IsDictionary()) {
    RefPtr<StructValue> ret_val = MakeRefCounted<StructValue>();

    VisionTK::Dictionary vtk_dict(vtk_value);
    std::vector<std::string> keys = vtk_dict.GetKeys();
    for (const auto& key : keys) {
      VisionTK::Value vtk_child_value = vtk_dict.Get(key);
      if (!CanHandleVtkValueType(vtk_child_value)) {
        continue;
      }
      // 递归转换子值
      RefPtr<CameraFeatureValue> media_child_value = VtkValueToMedia(vtk_child_value);
      // 添加键值对
      ret_val->addMember(key, media_child_value);
    }
    return ret_val;
  }

  return MakeRefCounted<CameraFeatureValue>();
}

VisionTK::Value CameraMethodHandler::MediaValueToVtk(RefPtr<CameraFeatureValue> media_value) {
  CameraValueType value_type = media_value->GetValueType();
  if (value_type == CameraValueType::kBool) {
    return VisionTK::Value(media_value->GetBool());
  } else if (value_type == CameraValueType::kInt8) {
    return VisionTK::Value(media_value->GetInt8());
  } else if (value_type == CameraValueType::kUInt8) {
    return VisionTK::Value(media_value->GetUInt8());
  } else if (value_type == CameraValueType::kInt16) {
    return VisionTK::Value(media_value->GetInt16());
  } else if (value_type == CameraValueType::kUInt16) {
    return VisionTK::Value(media_value->GetUInt16());
  } else if (value_type == CameraValueType::kInt32) {
    return VisionTK::Value(media_value->GetInt32());
  } else if (value_type == CameraValueType::kUInt32) {
    return VisionTK::Value(media_value->GetUInt32());
  } else if (value_type == CameraValueType::kInt64) {
    return VisionTK::Value(media_value->GetInt64());
  } else if (value_type == CameraValueType::kUInt64) {
    return VisionTK::Value(media_value->GetUInt64());
  } else if (value_type == CameraValueType::kFloat) {
    return VisionTK::Value(media_value->GetFloat());
  } else if (value_type == CameraValueType::kDouble) {
    return VisionTK::Value(media_value->GetDouble());
  } else if (value_type == CameraValueType::kStruct) {
    VisionTK::RefPtr<VisionTK::DictionaryImpl> dict = VisionTK::RefPtr<VisionTK::DictionaryImpl>::Make();

    VisionTK::Dictionary vtk_dict_value(dict);
    const auto& media_struct_map = media_value->GetStruct();
    for (const auto& kv_pair : media_struct_map) {
      std::string child_val_key = kv_pair.first;
      RefPtr<CameraFeatureValue> child_val_content = kv_pair.second;
      vtk_dict_value.Set(child_val_key, MediaValueToVtk(child_val_content));
    }
    return vtk_dict_value;
  }

  return VisionTK::Value();
}

void CameraMethodHandler::Handle_GetFeatures(const MethodCall<rapidjson::Document>& call,
                                             std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto cam_wrapper = cam_pool_.map_cameras_[sn];
  auto& vtk_cam = cam_wrapper->cam;
  cam_wrapper->feature_map.clear();

  std::vector<RefPtr<CameraFeature>> camera_feature_list;
  // Get camera features
  std::vector<VisionTK::Feature> device_features = vtk_cam.GetFeatures();
  ProcessVtkFeatureGroup(cam_wrapper, camera_feature_list, kDevice, device_features);

  // Get camera features
  std::vector<VisionTK::Feature> laser_features = vtk_cam.GetLaserComponent().GetFeatures();
  ProcessVtkFeatureGroup(cam_wrapper, camera_feature_list, "laser", laser_features);

  SensorType sensor_types[] = {SensorType::kDepth, SensorType::kRgb, SensorType::kIrLeft, SensorType::kIrRigth};
  for (const auto sensor_type : sensor_types) {
    auto sensor = GetSensor(vtk_cam, sensor_type);
    auto sensor_features = sensor.GetFeatures();
    ProcessVtkFeatureGroup(cam_wrapper, camera_feature_list, SensorTypeToString(sensor_type), sensor_features);
  }

  rapidjson::Value feature_array(rapidjson::kArrayType);
  for (const auto& media_feat : camera_feature_list) {
    rapidjson::Value feat_json = media_feat->SerializeToJsonObj(allocator);
    feature_array.PushBack(feat_json.Move(), allocator);
  }
  json_response.AddMember(kFeatureList, feature_array.Move(), allocator);

  SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
}

void CameraMethodHandler::Handle_GetFeatureValue(const MethodCall<rapidjson::Document>& call,
                                                 std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  int feat_id = (*args)[kFeatureId].GetInt();
  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto cam_wrapper = cam_pool_.map_cameras_[sn];
  // auto& vtk_cam = cam_wrapper->cam;
  if (cam_wrapper->feature_map.find(feat_id) == cam_wrapper->feature_map.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }
  auto vtk_feat = cam_wrapper->feature_map[feat_id];
  VisionTK::Status vtk_status = VisionTK::Status::Error;
  auto vtk_value = vtk_feat.GetValue(&vtk_status);
  if (vtk_status != VisionTK::Status::OK) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
  }
  auto media_value = VtkValueToMedia(vtk_value);
  rapidjson::Value json_feat_val = media_value->SerializeToJson(allocator);
  json_response.AddMember(kFeatureValue, json_feat_val.Move(), allocator);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
}

void CameraMethodHandler::Handle_SetFeature(const MethodCall<rapidjson::Document>& call,
                                            std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  int feat_id = (*args)[kFeatureId].GetInt();
  const rapidjson::Value& json_feat_value = (*args)[kFeatureValue];

  // std::string session_key = (*args)[kSessionKey].GetString();

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto cam_wrapper = cam_pool_.map_cameras_[sn];
  auto& feat_map = cam_wrapper->feature_map;
  if (feat_map.find(feat_id) == feat_map.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }
  auto& vtk_feat = feat_map[feat_id];
  RefPtr<CameraFeatureValue> refp_media_value = CameraFeatureValue::DeserializeFromJson(json_feat_value);
  VisionTK::Value vtk_value = MediaValueToVtk(refp_media_value);
  auto vtk_status = vtk_feat.SetValue(vtk_value);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, vtk_status);
}

void CameraMethodHandler::Handle_HasSensor(const MethodCall<rapidjson::Document>& call,
                                           std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();
  json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Failure), allocator);

  const rapidjson::Document* args = call.arguments();
  std::string sn = (*args)[kSn].GetString();
  SensorType sensor_type = static_cast<SensorType>((*args)[kSensorType].GetInt());

  //  Quick and dirty implementation
  if (cam_pool_.map_cameras_.find(sn) == cam_pool_.map_cameras_.end()) {
    SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::NotExist);
  }

  auto& cam = cam_pool_.map_cameras_[sn]->cam;
  bool has_sensor = false;
  switch (sensor_type) {
    case ucv::media::SensorType::kDepth:
      has_sensor = cam.HasDepthSensor();
      break;
    case ucv::media::SensorType::kIrLeft:
      has_sensor = cam.HasInfraredLeftSensor();
      break;
    case ucv::media::SensorType::kIrRigth:
      has_sensor = cam.HasInfraredRightSensor();
      break;
    case ucv::media::SensorType::kRgbLeft:
      has_sensor = cam.HasColorSensor();
      break;
    default:
      break;
  }
  json_response.AddMember(kExist, has_sensor, allocator);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, CameraApiStatus::Success);
}

void CameraMethodHandler::Handle_IsForceNetDeviceIP(const MethodCall<rapidjson::Document>& call,
                               std::shared_ptr<MethodResult<rapidjson::Document>>& result) {
  rapidjson::Document json_response;
  json_response.SetObject();
  auto& allocator = json_response.GetAllocator();

  const rapidjson::Value& params = *call.arguments();
  const char* mac = params["mac"].GetString();
  const char* ip = params["ip"].GetString();
  const char* mask = params["mask"].GetString();
  const char* gw = params["gateway"].GetString();

  VisionTK::Status status = VisionTK::ForceDeviceIP().IsForceNetDeviceIP(mac, ip, mask, gw);
  SEND_METHOD_RESULT_AND_RETURN(result, json_response, status);
}

void CameraMethodHandler::SendMethodResult(std::shared_ptr<MethodResult<rapidjson::Document>>& result,
                                           rapidjson::Document& json_response, VisionTK::Status vtk_status) {
  CameraApiStatus status = VtkStatusToCameraApiStatus(vtk_status);
  SendMethodResult(result, json_response, status);
}

void CameraMethodHandler::SendMethodResult(std::shared_ptr<MethodResult<rapidjson::Document>>& result,
                                           rapidjson::Document& json_response, CameraApiStatus status) {
  if (!json_response.HasMember(kStatus)) {
    json_response.AddMember(kStatus, CameraApiStatusToInt32(CameraApiStatus::Success), json_response.GetAllocator());
  }
  json_response[kStatus] = CameraApiStatusToInt32(status);
  result->Success(json_response);
}
}  // namespace media
}  // namespace ucv
