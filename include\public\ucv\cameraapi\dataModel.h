﻿#pragma once
#include "cameraCoreExport.h"
#include "ucv/base/object.h"

#include <sstream>
#include <string>

namespace ucv {
namespace media {
enum class CameraApiStatus : int32_t {
  Success,
  Failure,
  ArrayInfoInvalid,
  ArrayInvalid,
  CalibrationInfoInvalid,
  CameraInvalid,
  ComponentInvalid,
  DeviceInvalid,
  DeviceError,
  DeviceIdle,
  DeviceBusy,
  DeviceLost,
  DeviceInterfaceInvalid,
  DeviceInterfaceTypeError,
  DeviceInfoInvalid,
  FeatureInvalid,
  FeatureInfoInvalid,
  FeatureTypeError,
  FrameInvalid,
  FrameMetadataInvalid,
  FrameBufferInvalid,
  FrameBufferConsumerInvalid,
  FrameSetInvalid,
  FrameSetStreamInvalid,
  FrameSetConsumerInvalid,
  TriggerModeError,
  NotExist,
  NotImplemented,
  NotPermitted,
  NotSupported,
  OutOfMemory,
  OutOfIndexRange,
  OutOfValueRange,
  ParameterInvalid,
  StructureInfoInvalid,
  StructureInvalid,
  Timeout,
  ValueInvalid,
  ValueTypeError,
  ValueInfoInvalid,
};

std::string CameraApiStatusToMessage(CameraApiStatus status);

int32_t CameraApiStatusToInt32(CameraApiStatus status);
CameraApiStatus Int32ToCameraApiStatus(int32_t status_code);

enum class InterfaceType : int32_t {
  None = 0,
  USB = 1,
  Network = 2,
};

struct NetworkInfo {
  std::string mac;
  std::string ip;
  std::string netmask;
  std::string gateway;
  std::string broadcast;
};

// If you need IPv6 support:
struct NetworkInfoC {
  char mac[18] = {0};        // 17 chars + null terminator
  char ip[40] = {0};         // 39 chars + null terminator for IPv6
  char netmask[40] = {0};    // 39 chars + null terminator
  char gateway[40] = {0};    // 39 chars + null terminator
  char broadcast[40] = {0};  // 39 chars + null terminator
};

struct UsbInfo {
  int32_t bus;
  int32_t address;
};

enum class RawPixelFormat : int32_t {
  Undefined = 0,
  Mono,

  Bayer8GB,
  Bayer8BG,
  Bayer8GR,
  Bayer8RG,

  Bayer8GRBG,
  Bayer8RGGB,
  Bayer8GBRG,
  Bayer8BGGR,

  CSIMono10,

  CSIBayer10GRBG,
  CSIBayer10RGGB,
  CSIBayer10GBRG,
  CSIBayer10BGGR,

  CSIMono12,
  CSIBayer12GRBG,
  CSIBayer12RGGB,
  CSIBayer12GBRG,

  CSIBayer12BGGR,

  Depth16,
  YVYU,
  YUYV,
  Mono16,

  TOFIRMono16,

  RGB,
  BGR,
  JPEG,
  MJPG,

  RGB48,
  BGR48,
  XYZ48,

  // Ty Camera IMU
  IMU,

  // genicam
  Mono8,
  BayerGB8,
  BayerBG8,
  BayerGR8,
  BayerRG8,
  Mono10p,
  CSIMono10P,
  BayerGB10p,
  CSIBayerGB10P,
  BayerBG10p,
  CSIBayerBG10P,
  BayerGR10p,
  CSIBayerGR10P,
  BayerRG10p,
  CSIBayerRG10P,
  Mono12p,
  CSIMono12P,
  BayerGB12p,
  CSIBayerGB12P,
  BayerBG12p,
  CSIBayerBG12P,
  BayerGR12p,
  CSIBayerGR12P,
  BayerRG12p,
  CSIBayerRG12P,
  CSIMono14P,
  CSIBayerGB14P,
  CSIBayerBG14P,
  CSIBayerGR14P,
  CSIBayerRG14P,
  BayerGB16,
  BayerBG16,
  BayerGR16,
  BayerRG16,
  RGB8,
  BGR8,
  YUV422_8,
  YUV422_8_UYVY,
  YCbCr420_8_YY_CbCr_Planar,
  YCbCr420_8_YY_CrCb_Planar,
  YCbCr420_8_YY_CbCr_Semiplanar,
  YCbCr420_8_YY_CrCb_Semiplanar,
  Coord3D_C16,
  Coord3D_ABC16,
  Coord3D_ABC32f,
  TofIR_FourGroup_Mono16,
};

struct CAMERA_CORE_API DeviceInfo : public Object {
  UCV_DECLARE_CLASS(DeviceInfo, Object);

  static RefPtr<DeviceInfo> New();

  DeviceInfo() {
    interface_type = InterfaceType::None;
    usb_info.address = 0;
    usb_info.bus = 0;
  }

  InterfaceType interface_type;
  NetworkInfo network_info;
  UsbInfo usb_info;
  int status_code;
  bool is_opened_by_service;
  std::string sn;
  std::string name;
  std::string model_name;
  std::string vendor_name;
  std::string firmware_version;
  std::string config_version;
  std::string interface_IP;

  std::string ToString() const;
};

struct CAMERA_CORE_API DeviceInfoC {
  InterfaceType interface_type;
  NetworkInfoC network_info;
  UsbInfo usb_info;
  int status_code;
  uint8_t is_opened_by_service;
  char sn[64] = {0};
  char name[128] = {0};
  char model_name[128] = {0};
  char vendor_name[64] = {0};
  char firmware_version[512] = {0};
  char config_version[512] = {0};
  char interface_ip[32] = {0};
};

DeviceInfoC GetDeviceInfoC(RefPtr<DeviceInfo> refp_device_info);

enum class SensorType : int32_t { kDepth, kIrLeft, kIrRigth, kRgbLeft, kRgbRight, kRgb = kRgbLeft, kError = -1 };
std::string SensorTypeToString(SensorType sensor_type);

enum class GainType : int32_t {
  Unknown = -1,
  AnalogAll = 0,
  DigitalAll = 1,
  DigitalRed = 2,
  DigitalGreen = 3,
  DigitalBlue = 4,
};


enum class TriggerMode : int32_t { kOff, kHardware, kSoftware, kTriggerMode22 };

struct ImageMode {
 public:
  int32_t width;
  int32_t height;
  RawPixelFormat pixel_format;
};

struct CameraIntrinsic {
  int width;
  int height;

  float data[3 * 3];
};

struct CameraExtrinsic {
  float data[4 * 4];
};

struct CameraDistortion {
  float data[12];
};

struct CameraRotation {
  float data[9];
};

struct CalibInfo {
  CameraIntrinsic intrinsic;
  CameraExtrinsic extrinsic;
  CameraDistortion distortion;
};

enum class CameraStatus : int32_t {
  kOpened,
  kClosed,
  kCapturing,
  kOffline,
  kNotFound,
  kInvalid,
};

enum class CameraValueType : int32_t {
  kUndefined,

  kBool,

  kInt8,
  kUInt8,
  kInt16,
  kUInt16,

  kInt32,
  kUInt32,

  kInt64,
  kUInt64,

  kFloat,
  kDouble,

  kString,

  kArray,

  kStruct,
};

enum class CameraFeatureAccessMode : int32_t {
    kNotImplemented,
    kNotAvailable,
    kWriteOnly,
    kReadOnly,
    kReadWrite,
};
}  // namespace media
}  // namespace ucv
