﻿#pragma once
#include "ucv/base/logging.h"
#include "ucv/cameraapi/cameraCoreExport.h"

#ifdef _WIN32
#ifdef ucvcameraapi_EXPORTS
#define CAMERA_LOGGER_API __declspec(dllexport)
#else
#define CAMERA_LOGGER_API __declspec(dllimport)
#endif
#else
#define CAMERA_LOGGER_API
#endif

namespace ucv {
extern CAMERA_LOGGER_API logging::Logger cameraLogger;

}  // namespace ucv

#define UCV_CAMERA_LOGD(tag, fmt, ...) ::ucv::cameraLogger.log(ucv::LogLevel::Debug, tag, fmt, ##__VA_ARGS__)
#define UCV_CAMERA_LOGI(tag, fmt, ...) ::ucv::cameraLogger.log(ucv::LogLevel::Info, tag, fmt, ##__VA_ARGS__)
#define UCV_CAMERA_LOGW(tag, fmt, ...) ::ucv::cameraLogger.log(ucv::LogLevel::Warn, tag, fmt, ##__VA_ARGS__)
#define UCV_CAMERA_LOGE(tag, fmt, ...) ::ucv::cameraLogger.log(ucv::LogLevel::Error, tag, fmt, ##__VA_ARGS__)
#define UCV_CAMERA_LOGF(tag, fmt, ...) ::ucv::cameraLogger.log(ucv::LogLevel::Fatal, tag, fmt, ##__VA_ARGS__)
