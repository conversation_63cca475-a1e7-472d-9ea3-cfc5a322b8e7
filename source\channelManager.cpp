﻿#include "ucv/cameraapi/channelManager.h"

#include "serviceCoreResource.h"

#include "ucv/cameraapi/cameraApiConfig.h"
#include "ucv/cameraapi/jsonConstants.h"
#include "ucv/rpc/methodChannel.h"
#include "ucv/rpc/transportFactory.h"
#include "ucv/serviceapi/serviceApi.h"

#include <memory>

namespace ucv {
namespace media {
ChannelManager* ChannelManager::instance_;
std::once_flag ChannelManager::creation_flag_;

ChannelManager::ChannelManager() { init_cnt_ = 0; }

ChannelManager& ChannelManager::GetInstance() {
  std::call_once(creation_flag_, []() { instance_ = new ChannelManager(); });
  return *instance_;
}

void ChannelManager::Init() {
  if (CameraApiConfig::direct_call) {
    return;
  }
  std::lock_guard lock_guard(init_mutex_);

  if (init_cnt_ == 0) {
    p_service_core_ = std::make_unique<ServiceCoreResource>();
    p_service_core_->Init();
  }

  ++init_cnt_;
}

void ChannelManager::Release() {
  if (CameraApiConfig::direct_call) {
    return;
  }

  std::lock_guard lock_guard(init_mutex_);

  if (init_cnt_ == 0) {
    return;
  }

  --init_cnt_;
  // release underline resource
  if (init_cnt_ == 0) {
    p_service_core_.reset();
  }
}

void ChannelManager::InvokeMethod(const std::string& method, std::unique_ptr<rapidjson::Document> arguments,
                                  std::unique_ptr<MethodResult<rapidjson::Document>> result) {
  p_service_core_->method_channel->InvokeMethod(method, std::move(arguments), std::move(result));
}
}  // namespace media
}  // namespace ucv
