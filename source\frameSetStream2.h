﻿#pragma once
#include "VisionTK/Camera.h"
#include "VisionTK/Enums.h"
#include "VisionTK/Frame.h"
#include "VisionTK/IFrameSetStream.h"

using StdFuncVtkFrameSetRefCallback = std::function<void(VisionTK::FrameSet frame_set)>;

namespace ucv {
namespace media {
class FrameSetStream2 final {
 public:
  static FrameSetStream2 New(VisionTK::Camera camera, StdFuncVtkFrameSetRefCallback vtk_frame_set_cb, VisionTK::Status* status = nullptr);

 public:
  FrameSetStream2() = default;

  FrameSetStream2(const FrameSetStream2& other);
  FrameSetStream2(FrameSetStream2&& other);

  FrameSetStream2& operator=(const FrameSetStream2& other);
  FrameSetStream2& operator=(FrameSetStream2&& other);

  ~FrameSetStream2();

  bool Invalid() const noexcept;
  operator bool() const noexcept;

  inline friend bool operator==(const FrameSetStream2& lhs, const FrameSetStream2& rhs);

 private:
  friend class FrameBuffer;
  friend class VisionTK::RawPtr<FrameSetStream2>;

  explicit FrameSetStream2(VisionTK::RefPtr<VisionTK::IFrameSetConsumer>&&);

  VisionTK::IFrameSetConsumer* Ptr() const noexcept;

  VisionTK::IFrameSetConsumer* _ptr{nullptr};

  //StdFuncVtkFrameSetRefCallback vtk_frame_set_cb_;
};

}  // namespace media
}  // namespace ucv
