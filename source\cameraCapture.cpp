#include "ucv/cameraapi/cameraCapture.h"

#include "generalSignal.h"
#include "ucv/cameraapi/cameraLogging.h"
#include "ucv/cameraapi/channelManager.h"
#include "ucv/cameraapi/rawPixelBuffer.h"
// #include "ucv/aaldriver/imageProc.h"
#include "rapidjson/document.h"
#include "rapidjson/stringbuffer.h"  // 用于将JSON转换为字符串
#include "rapidjson/writer.h"        // 用于写入JSON
#include "ucv/base/serialization/sharedMemory.h"
#include "ucv/base/time/timePoint.h"
#include "ucv/cameraapi/cameraApiConfig.h"
#include "ucv/cameraapi/cameraApiUtils.h"
#include "ucv/cameraapi/cameraUtils.h"
#include "ucv/cameraapi/jsonConstants.h"
#include "ucv/cameraapi/postProcess.h"
#include "ucv/datamodel/image.h"
#include "ucv/rpc/methodResult.h"
#include "ucv/rpc/methodResultFunctions.h"

#include <rapidjson/prettywriter.h>
#include <string>

namespace ucv {
namespace media {
// Helper function to create the result handler - place this outside the class or in a utility section
std::unique_ptr<MethodResultFunctions<rapidjson::Document>> CreateStatusResultHandler(
    std::shared_ptr<StatusSignal> signal) {
  return std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* json_response) {
        int status_code = (*json_response)[kStatus].GetInt();
        signal->produce(Int32ToCameraApiStatus(status_code));
      },
      nullptr, nullptr);
}

void CameraCapture::ReleaseImageSharedMemory(std::string shared_memory_name) {
  // StatusSignal signal;

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSharedMemoryName, rapidjson::StringRef(shared_memory_name.c_str()), args->GetAllocator());
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSessionKey, rapidjson::StringRef(session_key_.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kReleaseImageSharedMemory, std::move(args), nullptr);

  //// 等待callback执行完毕
  // signal.consume();
  //// 取回 status 值并返回
  // return signal.data;
}

Array<RefPtr<CameraFeature>> CameraCapture::GetFeatures() {
  auto signal = std::make_shared<GeneralSignal<Array<RefPtr<CameraFeature>>>>();
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [this, signal](const rapidjson::Document* success_value) {
        ucv::Array<RefPtr<CameraFeature>> ret_val;
        const rapidjson::Document& doc = *success_value;

        bool print_features = true;
        if (print_features) {
          std::cout << "-------------------------------" << std::endl;
          rapidjson::StringBuffer buffer;
          rapidjson::PrettyWriter<rapidjson::StringBuffer> writer(buffer);
          success_value->Accept(writer);
          std::cout << buffer.GetString() << std::endl;
          std::cout << "-------------------------------" << std::endl;
        }

        // 检查 JSON 格式
        if (!doc.HasMember(kFeatureList) || !doc[kFeatureList].IsArray()) {
          signal->produce(ret_val);
          return;
        }

        const auto& feature_array_json = doc[kFeatureList];
        for (const auto& feature_json : feature_array_json.GetArray()) {
          auto camera_feature = CameraFeature::DeserializeFromJson(feature_json);
          if (camera_feature->name == "TY_STRUCT_TRIGGER_PARAM" ||
              camera_feature->name == "TY_STRUCT_TRIGGER_PARAM_EX") {
            continue;
          }
          ret_val.push_back(camera_feature);
        }

        signal->produce(ret_val);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSessionKey, rapidjson::StringRef(session_key_.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kGetFeatures, std::move(args), std::move(result_handler));

  bool is_data_valid = false;
  return signal->consume(is_data_valid);
}

struct GetFeatureContext {
 public:
  GetFeatureContext() { api_status = CameraApiStatus::Timeout; }
  CameraApiStatus api_status;
  RefPtr<CameraFeatureValue> feat_val;
};

CameraApiStatus CameraCapture::GetFeatureValue(int feat_id, RefPtr<CameraFeatureValue>& val) {
  auto signal = std::make_shared<GeneralSignal<GetFeatureContext>>();
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [this, signal](const rapidjson::Document* json_response) {
        GetFeatureContext signal_data;
        const rapidjson::Document& doc = *json_response;
        int status_code = doc[kStatus].GetInt();
        signal_data.api_status = Int32ToCameraApiStatus(status_code);

        if (signal_data.api_status != CameraApiStatus::Success) {
          signal->produce(signal_data);
        }

        auto json_feat_value = doc[kFeatureValue].GetObj();
        signal_data.feat_val = CameraFeatureValue::DeserializeFromJson(json_feat_value);
        signal->produce(signal_data);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  auto& allocator = args->GetAllocator();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), allocator);
  args->AddMember(kFeatureId, feat_id, allocator);

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kGetFeatureValue, std::move(args), std::move(result_handler));

  GetFeatureContext signal_value = signal->consume();
  val = signal_value.feat_val;
  return signal_value.api_status;
}

CameraApiStatus CameraCapture::SetFeature(int feat_id, RefPtr<CameraFeatureValue> refp_feat_val) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();

  auto& allocator = args->GetAllocator();
  rapidjson::Value json_feat_value = refp_feat_val->SerializeToJson(allocator);

  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), allocator);
  args->AddMember(kFeatureId, feat_id, allocator);
  args->AddMember(kFeatureValue, json_feat_value, allocator);

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kSetFeature, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

CameraApiStatus CameraCapture::SetFeature(int feat_id, std::string json_str_val) {
  rapidjson::Document doc;
  doc.Parse(json_str_val.c_str());

  RefPtr<CameraFeatureValue> refp_feat_val = CameraFeatureValue::DeserializeFromJson(doc);
  return SetFeature(feat_id, refp_feat_val);
}

CameraApiStatus CameraCapture::SetFeaturesFromFile(std::string file_path) {
  ucv::Array<RefPtr<CameraFeature>> feat_list;
  CameraApiStatus load_status = LoadFeaturesFromFile(file_path, feat_list);
  if (load_status != CameraApiStatus::Success) {
    return load_status;
  }

  ucv::Array<RefPtr<CameraFeature>> feat_list_to_set;

  // filter out non-writable features
  ucv::Array<CameraApiStatus> feat_list_set_status;
  for (const auto& feat : feat_list) {
    if (feat->access_mode == CameraFeatureAccessMode::kWriteOnly ||
        feat->access_mode == CameraFeatureAccessMode::kReadWrite) {
      feat_list_to_set.push_back(feat);
      feat_list_set_status.push_back(CameraApiStatus::Failure);
    }
  }
  if (feat_list_to_set.size() == 0) {
    ucv::cameraLogger.log(ucv::LogLevel::Warn, "", "Set features", "no feature to set");
    return CameraApiStatus::Success;
  }

  bool all_set_success = false;
  for (int i_retry = 0; i_retry < 10; i_retry++) {
    // loop and set feature
    for (int i_feat = 0; i_feat < feat_list_to_set.size(); ++i_feat) {
      if (feat_list_set_status[i_feat] == CameraApiStatus::Success) {
        continue;
      }
      auto feat = feat_list_to_set[i_feat];
      feat_list_set_status[i_feat] = SetFeature(feat->id, feat->refp_value);
    }

    all_set_success = true;
    // check set status
    for (int i_feat = 0; i_feat < feat_list_to_set.size(); ++i_feat) {
      if (feat_list_set_status[i_feat] != CameraApiStatus::Success) {
        all_set_success = false;
        break;
      }
    }
    if (all_set_success) {
      break;
    }
  }

  if (all_set_success) {
    ucv::cameraLogger.log(ucv::LogLevel::Info, "Set features", "all set");
  } else {
    std::string msg;
    for (int i = 0; i < feat_list_set_status.size(); ++i) {
      if (feat_list_set_status[i] != CameraApiStatus::Success) {
        if (!msg.empty()) {
          msg += ", ";
        }
        msg += feat_list_to_set[i]->name;
      }
    }
    msg += " (these features are not set)";
    ucv::cameraLogger.log(ucv::LogLevel::Error, "Set features", msg.c_str());
  }

  return all_set_success ? CameraApiStatus::Failure : CameraApiStatus::Success;
}

bool CameraCapture::HasSensor(SensorType sensor_type) {
  for (const auto& s : sensor_types_) {
    if (s == sensor_type) {
      return true;
    }
  }
  return false;
}

CameraApiStatus CameraCapture::IsForceNetDeviceIP(std::string mac, std::string ip, std::string mask,
                                                  std::string gateway) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kMacAddress, rapidjson::StringRef(mac.c_str()), args->GetAllocator());
  args->AddMember(kIP, rapidjson::StringRef(ip.c_str()), args->GetAllocator());
  args->AddMember(kMask, rapidjson::StringRef(mask.c_str()), args->GetAllocator());
  args->AddMember(kGateway, rapidjson::StringRef(gateway.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kIsForceNetDeviceIP, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}


void CameraCapture::ReceiveFrameSetLoop() {
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [this](const rapidjson::Document* success_value) {
        auto status = Int32ToCameraApiStatus((*success_value)[kStatus].GetInt());
        if (status != CameraApiStatus::Success) {
          ucv::cameraLogger.log(ucv::LogLevel::Error, "", "GetFrameSet fail, reason: %s",
                                CameraApiStatusToMessage(status).c_str());
          return;
        }

        // 尝试解析共享内存
        auto mem = SharedMemory::FromJson(*success_value);
        if (!mem) {
          ucv::cameraLogger.log(ucv::LogLevel::Error, "", "GetFrameSet fail to retrieve shared memory");
          return;
        }
        auto total_buffer_size = mem->GetSize();
        std::string guid = mem->GetName();
        ucv::cameraLogger.log(ucv::LogLevel::Info, "", "Get frame set ok, %d bytes, guid: %s", total_buffer_size,
                              guid.c_str());

        uint8_t* p_buffer_head = static_cast<uint8_t*>(mem->Get());
        RefPtr<FrameSet> refp_frame_set = MakeRefCounted<FrameSet>();
        const rapidjson::Value& frameArray = (*success_value)[kFrameInfoArray];
        for (const auto& obj : frameArray.GetArray()) {
          FrameInfo info;
          info.timestamp = obj[kTimestamp].GetUint64();
          info.frameIndex = obj[kFrameIndex].GetInt64();
          auto sensor_type = static_cast<SensorType>(obj[kSensorType].GetInt());
          info.sensor_type = sensor_type;

          // info.bufferSize = obj["bufferSize"].GetUint64();
          uint32_t width = obj[kWidth].GetInt();
          uint32_t height = obj[kHeight].GetInt();
          RawPixelFormat pixel_format = static_cast<RawPixelFormat>(obj[kPixelFormat].GetInt());
          uint64_t buffer_offset = obj[kBufferOffset].GetUint64();

          // 共享内存处理，转换为 image buffer
          uint8_t* p_buffer = p_buffer_head + buffer_offset;
          RefPtr<Image> image;
          float scale_unit = -1;
          switch (sensor_type) {
            case ucv::media::SensorType::kDepth:
              image = ConvertToRawDepthImage(p_buffer, width, height, pixel_format);
              scale_unit = (*success_value)[kScaleUnit].GetFloat();
              image->buffer()->SetScaleUnit(scale_unit);
              break;
            case ucv::media::SensorType::kIrLeft:
            case ucv::media::SensorType::kIrRigth:
              image = GetIrImage(p_buffer, width, height, pixel_format);
              break;
            case ucv::media::SensorType::kRgb:
              image = ConvertToBgrImage(p_buffer, width, height, pixel_format);
              break;
            default:
              break;
          }

          ParseJsonValueToCalibInfo(obj, info.calib_info);

          RefPtr<Frame> refp_frame = MakeRefCounted<Frame>();
          refp_frame->frame_info = info;
          refp_frame->image = image;
          refp_frame_set->frames.push_back(refp_frame);
        }
        // 共享内存使用完毕，释放共享内存
        ReleaseImageSharedMemory(guid);
        // 回调使用图像
        std::lock_guard<std::mutex> lock(frame_set_cb_mutex_);
        if (frame_set_cb_) {
          frame_set_cb_(refp_frame_set);
        }
      },
      nullptr, nullptr, true);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSessionKey, rapidjson::StringRef(session_key_.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kReceiveFrameSetLoop, std::move(args), std::move(result_handler));
}

void CameraCapture::ParseJsonValueToCalibInfo(const rapidjson::Value& json_obj, CalibInfo& calib_info) {
  // 解析 intrinsic
  {
    const auto& intrinsic_obj = json_obj[kIntrinsic];
    calib_info.intrinsic.width = intrinsic_obj[kWidth].GetInt();
    calib_info.intrinsic.height = intrinsic_obj[kHeight].GetInt();

    const auto& matrix_array = intrinsic_obj[kData];
    for (rapidjson::SizeType i = 0; i < 9; i++) {
      calib_info.intrinsic.data[i] = matrix_array[i].GetFloat();
    }
  }

  // 解析 extrinsic
  {
    const auto& extrinsic_obj = json_obj[kExtrinsic];
    const auto& matrix_array = extrinsic_obj[kData];
    for (rapidjson::SizeType i = 0; i < 16; i++) {
      calib_info.extrinsic.data[i] = matrix_array[i].GetFloat();
    }
  }

  // 解析 distortion
  {
    const auto& distortion_obj = json_obj[kDistortion];
    const auto& coeff_array = distortion_obj[kData];
    for (rapidjson::SizeType i = 0; i < 12; i++) {
      calib_info.distortion.data[i] = coeff_array[i].GetFloat();
    }
  }
}

CameraCapture::CameraCapture(std::string sn) { sn_ = sn; }

CameraStatus CameraCapture::GetCameraStatus() {
  auto signal = std::make_shared<GeneralSignal<CameraStatus>>();
  // 定义 callback
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* p_json_response) {
        auto& json_response = (*p_json_response);

        int cam_status_code = json_response[kCameraStatus].GetInt();
        CameraStatus cam_status = static_cast<CameraStatus>(cam_status_code);
        signal->produce(cam_status);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kGetCameraStatus, std::move(args), std::move(result_handler));

  // 等待callback执行完毕
  bool is_data_valid = false;
  auto cam_status = signal->consume(is_data_valid);
  if (is_data_valid) {
    return cam_status;
  }
  return CameraStatus::kOffline;
}

void CameraCapture::SetFrameSetCb(StdFuncFrameSetRefCallback cb) {
  std::lock_guard<std::mutex> lock(frame_set_cb_mutex_);
  frame_set_cb_ = cb;
}

struct ConnectContext {
  ConnectContext() { api_status = CameraApiStatus::Timeout; }
  CameraApiStatus api_status;
  std::vector<SensorType> sensor_types;
};

CameraApiStatus CameraCapture::Connect(std::string session_key) {
  if (session_key.empty()) {
    TimePoint time_point = TimePoint::Now();
    session_key = std::to_string(time_point());
  }

  auto signal = std::make_shared<GeneralSignal<ConnectContext>>();
  // 定义 callback
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* p_json_response) {
        ConnectContext signal_data;
        auto& json_response = (*p_json_response);
        int status_code = json_response[kStatus].GetInt();
        signal_data.api_status = Int32ToCameraApiStatus(status_code);
        if (signal_data.api_status == CameraApiStatus::Success) {
          for (const auto& item : json_response[kSensorTypes].GetArray()) {
            signal_data.sensor_types.push_back(static_cast<SensorType>(item.GetInt()));
          }
        }
        signal->produce(signal_data);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSessionKey, rapidjson::StringRef(session_key.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kConnect, std::move(args), std::move(result_handler));

  // 等待callback执行完毕
  auto signal_data = signal->consume(std::chrono::milliseconds(2000));
  if (signal_data.api_status == CameraApiStatus::Success) {
    session_key_ = session_key;
    sensor_types_ = signal_data.sensor_types;
  }
  return signal_data.api_status;
}

CameraApiStatus CameraCapture::Disconnect() {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSessionKey, rapidjson::StringRef(session_key_.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kDisconnect, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

CameraApiStatus CameraCapture::SetSensorEnabled(SensorType sensor_type, bool enabled) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSensorType, static_cast<int>(sensor_type), args->GetAllocator());
  args->AddMember(kEnableRequested, enabled, args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kSetSensorEnabled, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

struct SensorEnabledContext {
 public:
  SensorEnabledContext() {
    is_enalbed = false;
    api_status = CameraApiStatus::Timeout;
  }

  bool is_enalbed;
  CameraApiStatus api_status;
};

CameraApiStatus CameraCapture::IsSensorEnabled(SensorType sensor_type, bool& is_enabled) {
  auto signal = std::make_shared<GeneralSignal<SensorEnabledContext>>();
  // 定义 callback
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* p_json_response) {
        SensorEnabledContext signal_data;
        auto& json_response = (*p_json_response);
        int status_code = json_response[kStatus].GetInt();
        signal_data.api_status = Int32ToCameraApiStatus(status_code);
        if (signal_data.api_status == CameraApiStatus::Success) {
          signal_data.is_enalbed = json_response[kEnabled].GetBool();
        }
        signal->produce(signal_data);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSensorType, static_cast<int>(sensor_type), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kIsSensorEnabled, std::move(args), std::move(result_handler));

  // 等待callback执行完毕
  bool is_data_valid = false;
  auto signal_data = signal->consume(is_data_valid);
  is_enabled = signal_data.is_enalbed;
  return signal_data.api_status;
}

CameraApiStatus CameraCapture::SetTriggerMode(TriggerMode trigger_mode) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kTriggerMode, static_cast<int>(trigger_mode), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kSetTriggerMode, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

CameraApiStatus CameraCapture::SetFramePerTrigger(int num_frames) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kFramePerTrigger, static_cast<int>(num_frames), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kSetFramePerTrigger, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

CameraApiStatus CameraCapture::FireSoftwareTrigger() {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kFireSoftwareTrigger, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

CameraApiStatus CameraCapture::StartCapture() {
  ReceiveFrameSetLoop();

  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  /* args->AddMember(kSessionKey, rapidjson::StringRef(session_key.c_str()), args->GetAllocator());*/

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kStartCapture, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

CameraApiStatus CameraCapture::StopCapture() {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSessionKey, rapidjson::StringRef(session_key_.c_str()), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kStopCapture, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

struct GetImageModesContext {
  GetImageModesContext() { api_status = CameraApiStatus::Timeout; }
  CameraApiStatus api_status;
  Array<ImageMode> image_modes;
};

CameraApiStatus CameraCapture::GetImageModes(SensorType sensor_type, Array<ImageMode>& image_modes) {
  auto signal = std::make_shared<GeneralSignal<GetImageModesContext>>();

  // 定义 callback
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* json_response) {
        GetImageModesContext signal_data;
        int status_code = (*json_response)[kStatus].GetInt();
        signal_data.api_status = Int32ToCameraApiStatus(status_code);
        if (signal_data.api_status != CameraApiStatus::Success) {
          signal->produce(signal_data);
          return;
        }
        for (const auto& item : (*json_response)[kImageModes].GetArray()) {
          ImageMode mode;
          mode.width = item[kWidth].GetInt();
          mode.height = item[kHeight].GetInt();
          mode.pixel_format = static_cast<RawPixelFormat>(item[kPixelFormat].GetInt());
          signal_data.image_modes.push_back(mode);
        }
        signal->produce(signal_data);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  auto& allocator = args->GetAllocator();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), allocator);
  args->AddMember(kSensorType, static_cast<int32_t>(sensor_type), allocator);

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kGetImageModes, std::move(args), std::move(result_handler));

  // 等待callback执行完毕
  auto signal_data = signal->consume();
  if (signal_data.api_status == CameraApiStatus::Success) {
    image_modes = signal_data.image_modes;
  }
  return signal_data.api_status;
}

struct GetImageModeContext {
  GetImageModeContext() { api_status = CameraApiStatus::Timeout; }
  CameraApiStatus api_status;
  ImageMode image_mode;
};

CameraApiStatus CameraCapture::GetCurrentImageMode(SensorType sensor_type, ImageMode& image_mode) {
  auto signal = std::make_shared<GeneralSignal<GetImageModeContext>>();

  // 定义 callback
  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* p_json_response) {
        GetImageModeContext signal_data;
        auto& json_response = *p_json_response;
        int status_code = json_response[kStatus].GetInt();
        signal_data.api_status = Int32ToCameraApiStatus(status_code);
        if (signal_data.api_status != CameraApiStatus::Success) {
          signal->produce(signal_data);
          return;
        }
        signal_data.image_mode.width = json_response[kWidth].GetInt();
        signal_data.image_mode.height = json_response[kHeight].GetInt();
        signal_data.image_mode.pixel_format = static_cast<RawPixelFormat>(json_response[kPixelFormat].GetInt());
        signal->produce(signal_data);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  auto& allocator = args->GetAllocator();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), allocator);
  args->AddMember(kSensorType, static_cast<int32_t>(sensor_type), allocator);

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kGetCurrentImageMode, std::move(args), std::move(result_handler));

  // 等待callback执行完毕
  auto signal_data = signal->consume();
  if (signal_data.api_status == CameraApiStatus::Success) {
    image_mode = signal_data.image_mode;
  }
  return signal_data.api_status;
}

CameraApiStatus CameraCapture::SetImageMode(SensorType sensor_type, const ImageMode& mode) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  auto& allocator = args->GetAllocator();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), allocator);
  args->AddMember(kSensorType, static_cast<int>(sensor_type), allocator);
  args->AddMember(kWidth, mode.width, allocator);
  args->AddMember(kHeight, mode.height, allocator);
  args->AddMember(kPixelFormat, static_cast<int32_t>(mode.pixel_format), allocator);

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kSetImageMode, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

struct GetCalibInfoContext {
 public:
  GetCalibInfoContext() { api_status = CameraApiStatus::Timeout; }
  CameraApiStatus api_status;
  CalibInfo calib_info;
};

CameraApiStatus CameraCapture::GetCalibInfo(SensorType sensor_type, CalibInfo& calib_info) {
  auto signal = std::make_shared<GeneralSignal<GetCalibInfoContext>>();

  auto result_handler = std::make_unique<MethodResultFunctions<rapidjson::Document>>(
      [signal](const rapidjson::Document* p_json_response) {
        GetCalibInfoContext signal_data;
        auto& json_response = *p_json_response;
        int status_code = json_response[kStatus].GetInt();
        signal_data.api_status = Int32ToCameraApiStatus(status_code);
        if (signal_data.api_status == CameraApiStatus::Success) {
          ParseJsonValueToCalibInfo(json_response, signal_data.calib_info);
        }

        signal->produce(signal_data);
      },
      nullptr, nullptr);

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSensorType, static_cast<int>(sensor_type), args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kGetCalibInfo, std::move(args), std::move(result_handler));

  // 等待callback执行完毕
  auto signal_data = signal->consume();
  if (signal_data.api_status == CameraApiStatus::Success) {
    calib_info = signal_data.calib_info;
  }
  return signal_data.api_status;
}

CameraApiStatus CameraCapture::SetUndistortionEnabled(SensorType sensor_type, bool enable) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), args->GetAllocator());
  args->AddMember(kSensorType, static_cast<int>(sensor_type), args->GetAllocator());
  args->AddMember(kEnableRequested, enable, args->GetAllocator());

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kSetUndistortionEnabled, std::move(args),
                                             CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}

CameraApiStatus CameraCapture::SetGainValue(SensorType sensor_type, GainType gain_type, float gain) {
  auto signal = std::make_shared<StatusSignal>();

  // 参数构造
  auto args = std::make_unique<rapidjson::Document>();
  args->SetObject();
  auto& allocator = args->GetAllocator();
  args->AddMember(kSn, rapidjson::StringRef(sn_.c_str()), allocator);
  args->AddMember(kSensorType, static_cast<int>(sensor_type), allocator);
  args->AddMember(kGainType, static_cast<int>(gain_type), allocator);
  args->AddMember(kGainValue, gain, allocator);

  // IPC 调用方法
  ChannelManager::GetInstance().InvokeMethod(kSetGainValue, std::move(args), CreateStatusResultHandler(signal));

  // 等待callback执行完毕
  return signal->consume();
}
}  // namespace media
}  // namespace ucv
