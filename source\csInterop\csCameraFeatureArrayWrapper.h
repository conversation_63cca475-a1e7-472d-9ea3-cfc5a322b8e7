﻿#include "ucv/base/array.h"
#include "ucv/base/interop/csExport.h"
#include "ucv/base/interop/csInteropTypes.h"
#include "ucv/base/memory/refPtr.h"
#include "ucv/base/object.h"
#include "ucv/base/refPtr.h"
#include "ucv/cameraapi/dataModel.h"
#include "ucv/cameraapi/cameraFeature.h"

#include <iostream>
#include <string>
#include <vector>

using ucv::Array;
using ucv::RefPtr;

namespace ucv {
namespace media {
class CsCameraFeatureArrayWrapper : public ucv::Object {
  UCV_DECLARE_CLASS(CsCameraFeatureArrayWrapper, Object);

 public:
  CsCameraFeatureArrayWrapper(const Array<RefPtr<CameraFeature>>& deviceInfos);

  int GetCount() const;

 RefPtr<CameraFeature> GetFeatureByIndex(int index) const;

 private:
  Array<RefPtr<CameraFeature>> m_features;
};
}  // namespace media
}  // namespace ucv