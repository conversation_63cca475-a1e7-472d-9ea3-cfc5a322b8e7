﻿#include "ucv/cameraapi/cameraFeatureValue.h"
#include "ucv/cameraapi/jsonConstants.h"

namespace ucv {
namespace media {

RefPtr<CameraFeatureValue> CameraFeatureValue::DeserializeFromJson(const rapidjson::Value& value_json) {
  RefPtr<CameraFeatureValue> ret_val;
  auto value_type = static_cast<CameraValueType>(value_json[kType].GetInt());
  switch (value_type) {
    case ucv::media::CameraValueType::kUndefined:
    case ucv::media::CameraValueType::kString:
    case ucv::media::CameraValueType::kArray:
      break;
    case ucv::media::CameraValueType::kBool:
      return MakeRefCounted<BoolValue>(value_json[kContent].GetBool());
    case ucv::media::CameraValueType::kInt8:
      return MakeRefCounted<Int8Value>(value_json[kContent].GetInt());
    case ucv::media::CameraValueType::kUInt8:
      return MakeRefCounted<UInt8Value>(value_json[kContent].GetUint());
    case ucv::media::CameraValueType::kInt16:
      return MakeRefCounted<Int16Value>(value_json[kContent].GetInt());
    case ucv::media::CameraValueType::kUInt16:
      return MakeRefCounted<UInt16Value>(value_json[kContent].GetUint());
    case ucv::media::CameraValueType::kInt32:
      return MakeRefCounted<Int32Value>(value_json[kContent].GetInt());
    case ucv::media::CameraValueType::kUInt32:
      return MakeRefCounted<UInt32Value>(value_json[kContent].GetUint());
    case ucv::media::CameraValueType::kInt64:
      return MakeRefCounted<Int64Value>(value_json[kContent].GetInt64());
    case ucv::media::CameraValueType::kUInt64:
      return MakeRefCounted<UInt64Value>(value_json[kContent].GetUint64());
    case ucv::media::CameraValueType::kFloat:
      return MakeRefCounted<FloatValue>(value_json[kContent].GetFloat());
    case ucv::media::CameraValueType::kDouble:
      return MakeRefCounted<DoubleValue>(value_json[kContent].GetDouble());
    case ucv::media::CameraValueType::kStruct: {
      RefPtr<StructValue> struct_value = MakeRefCounted<StructValue>();
      // 遍历结构体的所有成员
      for (auto it = value_json[kContent].MemberBegin(); it != value_json[kContent].MemberEnd(); ++it) {
        const char* memberName = it->name.GetString();
        const rapidjson::Value& memberValue = it->value;
        struct_value->addMember(memberName, DeserializeFromJson(memberValue));
      }
      return struct_value;
    }
    default:
      break;
  }
  return MakeRefCounted<CameraFeatureValue>();
}

bool CameraFeatureValue::CanHandleValueType() {
  CameraValueType val_type = GetValueType();
  if (val_type == CameraValueType::kUndefined || val_type == CameraValueType::kString ||
      val_type == CameraValueType::kArray) {
    return false;
  }
  return true;
}

rapidjson::Value CameraFeatureValue::SerializeToJson(rapidjson::Document::AllocatorType& allocator) {
  if (!CanHandleValueType()) {
    return rapidjson::Value(rapidjson::kNullType);
  }

  rapidjson::Value ret_val(rapidjson::kObjectType);

  // Value type
  CameraValueType value_type = GetValueType();
  ret_val.AddMember(kType, static_cast<int32_t>(value_type), allocator);

  // 处理基本类型
  if (value_type == CameraValueType::kBool) {
    ret_val.AddMember(kContent, GetBool(), allocator);
  } else if (value_type == CameraValueType::kInt8) {
    ret_val.AddMember(kContent, GetInt8(), allocator);
  } else if (value_type == CameraValueType::kUInt8) {
    ret_val.AddMember(kContent, GetUInt8(), allocator);
  } else if (value_type == CameraValueType::kInt16) {
    ret_val.AddMember(kContent, GetInt16(), allocator);
  } else if (value_type == CameraValueType::kUInt16) {
    ret_val.AddMember(kContent, GetUInt16(), allocator);
  } else if (value_type == CameraValueType::kInt32) {
    ret_val.AddMember(kContent, GetInt32(), allocator);
  } else if (value_type == CameraValueType::kUInt32) {
    ret_val.AddMember(kContent, GetUInt32(), allocator);
  } else if (value_type == CameraValueType::kInt64) {
    ret_val.AddMember(kContent, GetInt64(), allocator);
  } else if (value_type == CameraValueType::kUInt64) {
    ret_val.AddMember(kContent, GetUInt64(), allocator);
  } else if (value_type == CameraValueType::kFloat) {
    ret_val.AddMember(kContent, GetFloat(), allocator);
  } else if (value_type == CameraValueType::kDouble) {
    ret_val.AddMember(kContent, GetDouble(), allocator);
  } else if (value_type == CameraValueType::kStruct) {
    rapidjson::Value json_dict(rapidjson::kObjectType);
    auto value_map = GetStruct();
    for (const auto& kv_pair : value_map) {
      std::string child_key = kv_pair.first;
      RefPtr<CameraFeatureValue> child_value = kv_pair.second;
      if (!child_value->CanHandleValueType()) {
        continue;
      }
      json_dict.AddMember(rapidjson::Value(child_key.c_str(), allocator),  // 键
                          child_value->SerializeToJson(allocator),         // 值
                          allocator);
    }
    ret_val.AddMember(kContent, json_dict.Move(), allocator);
  }

  return ret_val;
}
}  // namespace media
}  // namespace ucv
