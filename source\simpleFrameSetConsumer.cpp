﻿#include "SimpleFrameSetConsumer.h"

namespace ucv {
namespace media {
SimpleFrameSetConsumer::SimpleFrameSetConsumer() {}

SimpleFrameSetConsumer::~SimpleFrameSetConsumer() {}

VisionTK::Status SimpleFrameSetConsumer::ReceiveFrameSet(VisionTK::FrameSet frame_set) {
  try {
    cb_(frame_set);
    return VisionTK::Status::OK;
  } catch (...) {
    return VisionTK::Status::Error;
  }
}

void SimpleFrameSetConsumer::SetCallback(StdFuncVtkFrameSetRefCallback cb) { cb_ = cb; }

int32_t SimpleFrameSetConsumer::IncReferenceCount() { return _counter.Increase(); }

int32_t SimpleFrameSetConsumer::DecReferenceCount() { return _counter.Decrease(); }

void SimpleFrameSetConsumer::FreeReferencedObject() { delete this; }
}  // namespace media
}  // namespace ucv