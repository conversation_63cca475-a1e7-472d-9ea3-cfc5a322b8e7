﻿#pragma once
#include "cameraCoreExport.h"
#include "ucv/cameraapi/dataModel.h"
#include "ucv/cameraapi/cameraFeature.h"
#include "ucv/base/refPtr.h"
#include "ucv/base/array.h"
#include "ucv/datamodel/image.h"
#include "ucv/cameraapi/frameSet.h"

#include <functional>

namespace ucv {
namespace media {
/**
 * @brief 取到 frame set 后的回调方法.
 * @param frame_set 取到的 frame set，必须被 C++ 层 AdoptRef 或被 .NET Object 类进行管理
 */
typedef void (*CStyleFrameSetPtrCallback)(FrameSet* frame_set);

/**
 * @brief 取到 frame set 后的回调方法，C++ std::function wrapper.
 * @param frame_set 取到的 frame set
 */
using StdFuncFrameSetRefCallback = std::function<void(RefPtr<FrameSet> image)>;


class CAMERA_CORE_API ICameraCapture {
 public:
  CameraApiStatus virtual Connect(std::string sn) = 0;
  CameraApiStatus virtual Disconnect() = 0;
  void virtual SetFrameSetCb(StdFuncFrameSetRefCallback cb) = 0;
  //void virtual GetFrameSet(StdFuncFrameSetRefCallback frame_set_callback) = 0;
  CameraApiStatus virtual SetSensorEnabled(SensorType sensor_type, bool enabled) = 0;
  CameraApiStatus virtual IsSensorEnabled(SensorType sensor_type, bool& enabled) = 0;
  CameraApiStatus virtual SetTriggerMode(TriggerMode trigger_mode) = 0;
  CameraApiStatus virtual SetFramePerTrigger(int num_frames) = 0;
  CameraApiStatus virtual FireSoftwareTrigger() = 0;
  CameraStatus virtual GetCameraStatus() = 0;
  CameraApiStatus virtual StartCapture() = 0;
  CameraApiStatus virtual StopCapture() = 0;
  //CameraApiStatus virtual ClearFrameCache() = 0;
  CameraApiStatus virtual GetImageModes(SensorType sensor_type, Array<ImageMode>& resolutions) = 0;
  CameraApiStatus virtual GetCurrentImageMode(SensorType sensor_type, ImageMode& image_mode)  = 0;
  CameraApiStatus virtual SetImageMode(SensorType sensor_type, const ImageMode& mode) = 0;
  CameraApiStatus virtual GetCalibInfo(SensorType sensor_type, CalibInfo& calib_info) = 0;
  CameraApiStatus virtual SetUndistortionEnabled(SensorType sensor_type, bool enable) = 0;
  ucv::Array<RefPtr<CameraFeature>> virtual GetFeatures() = 0;
  CameraApiStatus virtual GetFeatureValue(int feat_id, RefPtr<CameraFeatureValue>& val) = 0;
  CameraApiStatus virtual SetFeature(int feat_id, RefPtr<CameraFeatureValue> refp_feat_val) = 0;
  CameraApiStatus virtual SetFeature(int feat_id, std::string json_str_val) = 0;
  CameraApiStatus virtual SetFeaturesFromFile(std::string file_path) = 0;
  bool virtual HasSensor(SensorType sensor_type) = 0;
  CameraApiStatus virtual IsForceNetDeviceIP(std::string mac, std::string ip, std::string mask, std::string gateway) = 0;
  CameraApiStatus virtual SetGainValue(SensorType sensor_type, GainType gain_type, float gain) = 0;
};
}  // namespace media
}  // namespace ucv
