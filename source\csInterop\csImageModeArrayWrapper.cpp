﻿#include "csImageModeArrayWrapper.h"
#include <iostream>
#include <string>
#include <vector>

using ucv::Array;
using ucv::RefPtr;
namespace ucv {
namespace media {
CsImageModeArrayWrapper::CsImageModeArrayWrapper(const Array<ImageMode>& image_modes) : m_image_modes(image_modes) {}

int CsImageModeArrayWrapper::GetCount() const { return m_image_modes.size(); }

ImageMode CsImageModeArrayWrapper::GetImageMode(int index) const { return m_image_modes[index]; }
}  // namespace media
}  // namespace ucv