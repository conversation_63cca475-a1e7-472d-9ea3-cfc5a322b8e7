﻿#include "csCameraFeatureArrayWrapper.h"

#include <iostream>
#include <string>
#include <vector>

using ucv::Array;
using ucv::RefPtr;
namespace ucv {
namespace media {
CsCameraFeatureArrayWrapper::CsCameraFeatureArrayWrapper(const Array<RefPtr<CameraFeature>>& feature_list)
    : m_features(feature_list) {}

int CsCameraFeatureArrayWrapper::GetCount() const { return m_features.size(); }

RefPtr<CameraFeature> CsCameraFeatureArrayWrapper::GetFeatureByIndex(int index) const { return m_features[index]; }
}  // namespace media
}  // namespace ucv