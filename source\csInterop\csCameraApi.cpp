﻿#include "csDeviceInfoArrayWrapper.h"
#include "csCameraFeatureArrayWrapper.h"
#include "csImageModeArrayWrapper.h"
#include "ucv/aaldriver/pointCloudProc.h"
#include "ucv/base/array.h"
#include "ucv/base/interop/csExport.h"
#include "ucv/base/interop/csInteropTypes.h"
#include "ucv/base/memory/refPtr.h"
#include "ucv/base/object.h"
#include "ucv/base/refPtr.h"
#include "ucv/cameraapi/cameraCapture.h"
#include "ucv/cameraapi/cameraUtils.h"
#include "ucv/cameraapi/channelManager.h"
#include "ucv/cameraapi/dataModel.h"
#include "ucv/cameraapi/postProcess.h"
#include "ucv/cameraapi/cameraApiConfig.h"

#include <iostream>
#include <string>
#include <vector>

using ucv::Array;
using ucv::RefPtr;

namespace ucv {
namespace media {

UCVCS_API(void) UCV_CameraApi_UseDirectCall(uint8_t use_direct_call) {
  CameraApiConfig::direct_call = use_direct_call != 0;
}

UCVCS_API(void) UCV_CameraApi_InitChannel() { ChannelManager::GetInstance().Init(); }
UCVCS_API(void) UCV_CameraApi_CloseChannel() { ChannelManager::GetInstance().Release(); }

/**
 * @brief 枚举设备.
 *
 * \return 设备列表 wrapper 指针
 */
UCVCS_API(CsDeviceInfoArrayWrapper*) UCV_CameraApi_EnumerateDevices() {
  auto deviceInfoList = EnumerateDevices();
  RefPtr<CsDeviceInfoArrayWrapper> refpArrayWrapper = MakeRefCounted<CsDeviceInfoArrayWrapper>(deviceInfoList);
  return refpArrayWrapper.Detach();
}

/**
 * @brief 获取设备列表的长度.
 *
 * \param pArrayWrapper
 * \return 设备数量
 */
UCVCS_API(int) UCV_CameraApi_GetDeviceInfoArrayCount(CsDeviceInfoArrayWrapper* pArrayWrapper) {
  RefPtr<CsDeviceInfoArrayWrapper> refpArrayWrapper(pArrayWrapper);
  return refpArrayWrapper->GetCount();
}

/**
 * @brief 从设备列表中获取设备信息.
 *
 * \param pArrayWrapper
 * \param index 设备在列表中的 index（zero-based）
 * \return 设备信息
 */
UCVCS_API(DeviceInfoC) UCV_CameraApi_GetDeviceInfo(CsDeviceInfoArrayWrapper* pArrayWrapper, int index) {
  RefPtr<CsDeviceInfoArrayWrapper> refp_array_wrapper(pArrayWrapper);
  RefPtr<DeviceInfo> refp_device_info = refp_array_wrapper->GetDeviceInfo(index);
  DeviceInfoC device_info_c = GetDeviceInfoC(refp_device_info);
  return device_info_c;
}

UCVCS_API(CameraCapture*)
UCV_CameraApi_CameraCapture_New(const char* sn) {
  /* RefPtr<CameraCapture> refp_cc =
 MakeRefCounted<CameraCapture>(sn);
 return refp_cc.Detach();*/
  return new CameraCapture(sn);
}

UCVCS_API(void)
UCV_CameraApi_CameraCapture_SetFrameSetCallback(CameraCapture* p_camera_capture, CStyleFrameSetPtrCallback callback) {
  if (!callback) {
    // 如果回调为空，直接调用原函数
    p_camera_capture->SetFrameSetCb(StdFuncFrameSetRefCallback(nullptr));
    return;
  }

  // 包装回调函数
  auto wrapper = [callback](RefPtr<FrameSet> refp_frame_set) {
    if (refp_frame_set) {
      // 注意：这里使用 Detach 来释放 RefPtr 的所有权
      callback(refp_frame_set.Detach());
    } else {
      callback(nullptr);
    }
  };

  // 调用原函数
  p_camera_capture->SetFrameSetCb(std::move(wrapper));
}

UCVCS_API(CameraStatus) UCV_CameraApi_GetCameraStatus(CameraCapture* p_cam_capture) {
  return p_cam_capture->GetCameraStatus();
}

UCVCS_API(CameraApiStatus) UCV_CameraApi_CameraCapture_Connect(CameraCapture* pCameraCapture) {
  return pCameraCapture->Connect("");
}

UCVCS_API(CameraApiStatus) UCV_CameraApi_CameraCapture_Disconnect(CameraCapture* pCameraCapture) {
  return pCameraCapture->Disconnect();
}

UCVCS_API(CsCameraFeatureArrayWrapper*) UCV_CameraApi_GetFeatures(CameraCapture* pCameraCapture) {
  Array<RefPtr<CameraFeature>> features = pCameraCapture->GetFeatures();
  RefPtr<CsCameraFeatureArrayWrapper> refpArrayWrapper = MakeRefCounted<CsCameraFeatureArrayWrapper>(features);
  return refpArrayWrapper.Detach();
}

UCVCS_API(int) UCV_CameraApi_GetFeatureArrayCount(CsCameraFeatureArrayWrapper* p_features) {
  RefPtr<CsCameraFeatureArrayWrapper> refp_feature_array(p_features);
  return refp_feature_array->GetCount();
}

UCVCS_API(CameraFeature*) UCV_CameraApi_GetFeatureFromArrayByIndex(CsCameraFeatureArrayWrapper* p_features, int index) {
  RefPtr<CsCameraFeatureArrayWrapper> refp_feature_array(p_features);
  RefPtr<CameraFeature> refp_feat = refp_feature_array->GetFeatureByIndex(index);
  return refp_feat.Detach();
}

UCVCS_API(const char*) UCV_CameraApi_GetFeatureJsonStr(CameraFeature* p_feature) {
  RefPtr<CameraFeature> refp_feat(p_feature);
  return refp_feat->GetJsonStr().c_str();
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_SetFeatureJsonStr(CameraCapture* p_camera, int feat_id, const char* json_str_value) {
  CameraApiStatus status = p_camera->SetFeature(feat_id, json_str_value);
  return status;
}

/**
 * @brief 获取 sensor 支持的 image modes.
 *
 * \param pCameraCapture
 * \param sensor_type
 * \param api_status
 * \return
 */
UCVCS_API(CsImageModeArrayWrapper*)
UCV_CameraApi_CameraCapture_GetImageModes(CameraCapture* pCameraCapture, SensorType sensor_type,
                                          CameraApiStatus* api_status) {
  Array<ImageMode> image_modes;
  *api_status = pCameraCapture->GetImageModes(sensor_type, image_modes);
  RefPtr<CsImageModeArrayWrapper> refp_array_wrapper = MakeRefCounted<CsImageModeArrayWrapper>(image_modes);
  return refp_array_wrapper.Detach();
}

/**
 * @brief 从列表中获取 image mode 数量.
 *
 * \param pArrayWrapper
 * \return 列表中的 image mode 数量
 */
UCVCS_API(int) UCV_CameraApi_CameraCapture_GetImageModeCount(CsImageModeArrayWrapper* pArrayWrapper) {
  RefPtr<CsImageModeArrayWrapper> refpArrayWrapper(pArrayWrapper);
  return refpArrayWrapper->GetCount();
}

/**
 * @brief 从列表中获取 image mode.
 *
 * \param pArrayWrapper
 * \param index image mode 在列表中的 index（zero-based）
 * \return
 */
UCVCS_API(ImageMode) UCV_CameraApi_GetImageModeFromArray(CsImageModeArrayWrapper* pArrayWrapper, int index) {
  RefPtr<CsImageModeArrayWrapper> refp_array_wrapper(pArrayWrapper);
  return refp_array_wrapper->GetImageMode(index);
}

UCVCS_API(ImageMode)
UCV_CameraApi_GetCurrentImageMode(CameraCapture* pCameraCapture, SensorType sensor_type, CameraApiStatus* api_status) {
  ImageMode mode;
  *api_status = pCameraCapture->GetCurrentImageMode(sensor_type, mode);
  return mode;
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_SetImageMode(CameraCapture* pCameraCapture, SensorType sensor_type, ImageMode mode) {
  return pCameraCapture->SetImageMode(sensor_type, mode);
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_CameraCapture_SetSensorEnabled(CameraCapture* pCameraCapture, SensorType sensor_type, uint8_t enabled) {
  return pCameraCapture->SetSensorEnabled(sensor_type, enabled != 0);
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_CameraCapture_IsForceNetDeviceIP(CameraCapture* pCameraCapture, 
                                               const char* mac, 
                                               const char* ip,                                  
                                               const char* mask,                                
                                               const char* gateway) {
  return pCameraCapture->IsForceNetDeviceIP(mac, ip, mask, gateway);
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_CameraCapture_IsSensorEnabled(CameraCapture* pCameraCapture, SensorType sensor_type, uint8_t& enabled) {
  bool b_enabled;
  CameraApiStatus status = pCameraCapture->IsSensorEnabled(sensor_type, b_enabled);
  enabled = b_enabled ? 1 : 0;
  return status;
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_CameraCapture_SetTriggerMode(CameraCapture* pCameraCapture, TriggerMode triggerMode) {
  return pCameraCapture->SetTriggerMode(triggerMode);
}

UCVCS_API(CameraApiStatus) UCV_CameraApi_CameraCapture_FireSoftwareTrigger(CameraCapture* pCameraCapture) {
  return pCameraCapture->FireSoftwareTrigger();
}

UCVCS_API(CameraApiStatus) UCV_CameraApi_CameraCapture_StartCapture(CameraCapture* pCameraCapture) {
  return pCameraCapture->StartCapture();
}

UCVCS_API(CameraApiStatus) UCV_CameraApi_CameraCapture_StopCapture(CameraCapture* pCameraCapture) {
  return pCameraCapture->StopCapture();
}

//UCVCS_API(CameraApiStatus) UCV_CameraApi_CameraCapture_ClearFrameCache(CameraCapture* pCameraCapture) {
//  return pCameraCapture->ClearFrameCache();
//}

//UCVCS_API(void)
//UCV_CameraApi_CameraCapture_GetFrameSet(CameraCapture* pCameraCapture, CStyleFrameSetPtrCallback callback) {
//  if (!callback) {
//    // 如果回调为空，直接调用原函数
//    pCameraCapture->GetFrameSet(StdFuncFrameSetRefCallback(nullptr));
//    return;
//  }
//
//  // 包装回调函数
//  auto wrapper = [callback](RefPtr<FrameSet> ref_image) {
//    if (ref_image) {
//      // 注意：这里使用 Detach 来释放 RefPtr 的所有权
//      callback(ref_image.Detach());
//    } else {
//      callback(nullptr);
//    }
//  };
//
//  // 调用原函数
//  pCameraCapture->GetFrameSet(std::move(wrapper));
//}

UCVCS_API(Frame*) UCV_CameraApi_FrameSet_GetFrame(FrameSet* p_frame_set, SensorType sensor_type) {
  RefPtr<Frame> frame = p_frame_set->GetFrame(sensor_type);
  return frame.Detach();
}

UCVCS_API(Image*) UCV_CameraApi_Frame_GetImage(Frame* p_frame) {
  RefPtr<Frame> refp_frame(p_frame);
  RefPtr<Image> image = refp_frame->image;
  return image.Detach();
}

UCVCS_API(FrameInfo) UCV_CameraApi_Frame_GetInfo(Frame* p_frame) {
  RefPtr<Frame> refp_frame(p_frame);
  FrameInfo info = refp_frame->frame_info;
  return info;
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_CameraCapture_GetCalibInfo(CameraCapture* pCameraCapture, SensorType sensor_type, CalibInfo& calib_info) {
  return pCameraCapture->GetCalibInfo(sensor_type, calib_info);
}

UCVCS_API(CameraApiStatus)
UCV_CameraApi_CameraCapture_SetUndistortionEnalbed(CameraCapture* pCameraCapture, SensorType sensor_type, bool enable) {
  return pCameraCapture->SetUndistortionEnabled(sensor_type, enable);
}

UCVCS_API(Image*) UCV_CameraApi_PostProcess_RenderDepthImage(Image* p_image, float min_depth, float max_depth) {
  RefPtr<Image> refp_image(p_image);
  RefPtr<Image> refp_rendered_image = ucv::media::RenderDepthImage(refp_image, min_depth, max_depth);
  return refp_rendered_image.Detach();
}

UCVCS_API(Image*) UCV_CameraApi_ConvertGray16To8(Image* p_image) {
  RefPtr<Image> refp_gray16(p_image);
  RefPtr<Image> refp_gray8 = ucv::media::ConvertGray16To8(refp_gray16);
  return refp_gray8.Detach();
}

UCVCS_API(Image*) UCV_CameraApi_ConvertDepth16To32F(Image* p_image) {
  RefPtr<Image> refp_depth16(p_image);
  RefPtr<Image> refp_depth32f = ucv::media::ConvertDepth16To32F(refp_depth16);
  return refp_depth32f.Detach();
}

UCVCS_API(PointCloud*)
UCV_CameraApi_PostProcess_DepthToPointCloud(Image* depth, float intrinsic[9], StatusCode* status_code,
                                            uint8_t organized) {
  RefPtr<Image> refp_depth(depth);
  Matrix3d intrinsic_matrix = Matrix3d(intrinsic[0], intrinsic[1], intrinsic[2],   // row0
                                       intrinsic[3], intrinsic[4], intrinsic[5],   // row1
                                       intrinsic[6], intrinsic[7], intrinsic[8]);  // row2
  Status status;

  RefPtr<PointCloud> point_cloud =
      ucv::aal::pointCloudProcess::convertDepthToPointCloud(refp_depth, intrinsic_matrix, &status, organized != 0);
  *status_code = status.code();
  return point_cloud.Detach();
}

UCVCS_API(uint8_t)
UCV_CameraApi_HasSensor(CameraCapture* pCameraCapture, SensorType sensor_type) {
  return pCameraCapture->HasSensor(sensor_type) ? 1 : 0;
}
}  // namespace media
}  // namespace ucv
